import { useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { $api } from '@/lib/api/client';


/**
 * Hook for fetching user notifications with pagination and filtering.
 * Supports filtering by read status and notification type.
 * Uses unified endpoint that works for both internal and external users.
 *
 * @param page - Page number (0-based)
 * @param size - Page size (1-100)
 * @param filters - Filtering options
 * @param filters.status - Filter by read status: 'all', 'unread', 'read'
 * @param filters.type - Filter by notification type (frontend filtering until backend supports it)
 */
export function useNotifications(
  page = 0,
  size = 20,
  filters: {
    status?: 'all' | 'unread' | 'read';
  } = {}
) {
  const { status = 'all' } = filters;

  // Convert status filter to backend-compatible unreadOnly parameter
  const unreadOnly = status === 'unread';

  return $api.useQuery('get', '/api/notifications', {
    params: {
      query: {
        page,
        size,
        unreadOnly,
      },
    },
  }, {
  });
}

/**
 * Hook for fetching unread notification count.
 * Used for displaying notification badges.
 */
export function useUnreadNotificationCount() {
  return $api.useQuery('get', '/api/notifications/unread-count');
}

/**
 * Hook for marking a notification as read.
 * Provides cache invalidation and optimistic updates.
 */
export function useMarkNotificationAsRead() {
  const queryClient = useQueryClient();

  return $api.useMutation('post', '/api/notifications/mark-read', {
    onSuccess: () => {
      // Invalidate notifications and unread count
      queryClient.invalidateQueries({ queryKey: ['get', '/api/notifications'] });
      queryClient.invalidateQueries({ queryKey: ['get', '/api/notifications/unread-count'] });
    },
    onError: () => {
      toast.error('Failed to mark notification as read');
    },
  });
}

/**
 * Hook for marking all notifications as read.
 * Provides cache invalidation and success feedback.
 */
export function useMarkAllNotificationsAsRead() {
  const queryClient = useQueryClient();

  return $api.useMutation('post', '/api/notifications/mark-all-read', {
    onSuccess: (data) => {
      // Invalidate notifications and unread count
      queryClient.invalidateQueries({ queryKey: ['get', '/api/notifications'] });
      queryClient.invalidateQueries({ queryKey: ['get', '/api/notifications/unread-count'] });

      // Show success toast with count
      const count = data || 0;
      if (count > 0) {
        toast.success(`Marked ${count} notifications as read`);
      } else {
        toast.info('No unread notifications to mark');
      }
    },
    onError: () => {
      toast.error('Failed to mark notifications as read');
    },
  });
}
