import { $api } from '@/lib/api/client';
import { useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

/**
 * Custom hook for soft deleting a brand.
 * Uses openapi-react-query for type-safe API calls with React Query.
 *
 * Automatically invalidates the brands list query on success
 * to ensure the UI removes the deleted brand from the list.
 */
export function useDeleteBrand() {
  const queryClient = useQueryClient();

  return $api.useMutation('delete', '/api/brands/{id}', {
    onSuccess: () => {
      // Invalidate and refetch all brands list queries (regardless of parameters)
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/brands'],
      });

      // Show success toast notification
      toast.success('Brand deleted successfully');
    },
    onError: (error) => {
      console.error('Failed to delete brand:', error);
      toast.error('Failed to delete brand');
    },
  });
}
