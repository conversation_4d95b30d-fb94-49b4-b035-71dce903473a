import { $api } from '@/lib/api/client';
import { useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

/**
 * Custom hook for creating a new brand.
 * Uses openapi-react-query for type-safe API calls with React Query.
 *
 * Automatically invalidates the brands list query on success
 * to ensure the UI shows the newly created brand.
 */
export function useCreateBrand() {
  const queryClient = useQueryClient();

  return $api.useMutation('post', '/api/brands', {
    onSuccess: () => {
      // Invalidate and refetch all brands list queries (regardless of parameters)
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/brands'],
      });

      // Show success toast notification
      toast.success('Brand created successfully');
    },
    onError: (error) => {
      console.error('Failed to create brand:', error);
      toast.error('Failed to create brand');
    },
  });
}
