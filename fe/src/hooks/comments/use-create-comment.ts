import { $api } from '@/lib/api/client';
import { useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { useTranslations } from '@/lib/i18n/typed-translations';

/**
 * Custom hook for creating a new comment on a post.
 * Uses openapi-react-query for type-safe API calls with React Query.
 *
 * Automatically invalidates the comments list query and post details on success
 * to ensure the UI shows the newly created comment and updated comment count.
 *
 * The backend automatically:
 * - Scopes the comment to the current account (multi-tenancy)
 * - Sets the author to the current user
 * - Validates comment content
 * - Updates the post's comment count
 */
export function useCreateComment() {
  const queryClient = useQueryClient();
  const { t, keys } = useTranslations();

  return $api.useMutation('post', '/api/posts/{postId}/comments', {
    onSuccess: (_, variables) => {
      const postId = variables.params.path.postId;

      // Invalidate and refetch all comments list queries for this post (regardless of parameters)
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/posts/{postId}/comments', { params: { path: { postId } } }],
      });

      // Invalidate post details to update comment count
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/posts/{postId}', { params: { path: { postId } } }],
      });

      // Invalidate posts list to update comment count in list view
      queryClient.invalidateQueries({
        predicate: (query) => {
          const queryKey = query.queryKey as unknown[];
          return queryKey.length >= 2 &&
                 queryKey[0] === 'get' &&
                 queryKey[1] === '/api/hubs/{hubId}/posts';
        }
      });

      // Show success toast notification
      toast.success(t(keys.collaborationHubs.posts.comments.commentPosted));
    },
    onError: (error) => {
      console.error('Failed to create comment:', error);
      toast.error(t(keys.collaborationHubs.posts.comments.failedToPost));
    },
  });
}
