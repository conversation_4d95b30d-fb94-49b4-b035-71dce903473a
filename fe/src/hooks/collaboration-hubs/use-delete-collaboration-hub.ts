import { $api } from '@/lib/api/client';
import { useQueryClient } from '@tanstack/react-query';

/**
 * Custom hook for deleting a collaboration hub.
 * Uses openapi-react-query for type-safe API calls with React Query.
 * 
 * Automatically invalidates the collaboration hubs list query on success
 * to ensure the UI removes the deleted hub.
 * 
 * Only admins can delete collaboration hubs.
 * Deleting a hub removes all associated data including participants, posts, and chat messages.
 */
export function useDeleteCollaborationHub() {
  const queryClient = useQueryClient();

  return $api.useMutation('delete', '/api/hubs/{id}', {
    onSuccess: (_, variables) => {
      // Invalidate and refetch all collaboration hubs list queries (regardless of parameters)
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/hubs'],
      });
      
      // Remove the specific hub from cache
      queryClient.removeQueries({
        queryKey: ['get', '/api/hubs/{id}', { params: { path: { id: variables.params.path.id } } }],
      });
      
      // Remove all related queries for this hub
      queryClient.removeQueries({
        predicate: (query) => {
          const queryKey = query.queryKey as unknown[];
          if (queryKey.length < 2) return false;
          
          // Remove hub participants queries
          if (queryKey[1] === '/api/hubs/{hubId}/participants') {
            const params = queryKey[2] as { params?: { path?: { hubId?: number } } };
            return params?.params?.path?.hubId === variables.params.path.id;
          }
          
          // Remove hub posts queries
          if (queryKey[1] === '/api/hubs/{hubId}/posts') {
            const params = queryKey[2] as { params?: { path?: { hubId?: number } } };
            return params?.params?.path?.hubId === variables.params.path.id;
          }
          
          // Remove hub briefs queries
          if (queryKey[1] === '/api/hubs/{hubId}/briefs') {
            const params = queryKey[2] as { params?: { path?: { hubId?: number } } };
            return params?.params?.path?.hubId === variables.params.path.id;
          }
          
          // Remove hub chat queries
          if (queryKey[1] === '/api/hubs/{hubId}/chat/channels') {
            const params = queryKey[2] as { params?: { path?: { hubId?: number } } };
            return params?.params?.path?.hubId === variables.params.path.id;
          }
          
          return false;
        }
      });
    },
  });
}
