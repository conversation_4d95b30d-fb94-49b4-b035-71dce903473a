import { $api } from '@/lib/api/client';
import { useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { useTranslations } from '@/lib/i18n/typed-translations';

/**
 * Custom hook for creating a new collaboration hub.
 * Uses openapi-react-query for type-safe API calls with React Query.
 *
 * Automatically invalidates the collaboration hubs list query on success
 * to ensure the UI shows the newly created hub.
 *
 * The backend automatically:
 * - Scopes the hub to the current account (multi-tenancy)
 * - Adds the creator as an admin participant
 * - Creates default chat channels
 */
export function useCreateCollaborationHub() {
  const queryClient = useQueryClient();
  const { t, keys } = useTranslations();

  return $api.useMutation('post', '/api/hubs', {
    onSuccess: () => {
      // Invalidate and refetch all collaboration hubs list queries (regardless of parameters)
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/hubs'],
      });

      // Show success toast notification
      toast.success(t(keys.collaborationHubs.createDialog.successMessage));
    },
    onError: (error) => {
      console.error('Failed to create collaboration hub:', error);
      toast.error(t(keys.collaborationHubs.createDialog.errorMessage));
    },
  });
}
