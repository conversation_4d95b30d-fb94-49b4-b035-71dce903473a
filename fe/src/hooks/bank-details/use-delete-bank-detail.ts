import { $api } from '@/lib/api/client';
import { useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

/**
 * Custom hook for deleting a bank detail (soft delete).
 * Uses openapi-react-query for type-safe API calls with React Query.
 *
 * Automatically invalidates the bank details list query on success
 * to ensure the UI removes the deleted bank detail.
 */
export function useDeleteBankDetail() {
  const queryClient = useQueryClient();

  return $api.useMutation('delete', '/api/bank-details/{id}', {
    onSuccess: () => {
      // Invalidate and refetch all bank details list queries (regardless of parameters)
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/bank-details'],
      });

      // Show success toast notification
      toast.success('Bank detail deleted successfully');
    },
    onError: (error) => {
      console.error('Failed to delete bank detail:', error);
      toast.error('Failed to delete bank detail');
    },
  });
}
