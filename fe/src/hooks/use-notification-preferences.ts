import { useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { $api } from '@/lib/api/client';

/**
 * Hook for fetching user notification preferences.
 * Automatically creates default preferences if none exist.
 */
export function useNotificationPreferences() {
  return $api.useQuery('get', '/api/notification-preferences');
}

/**
 * Hook for updating user notification preferences.
 * Provides optimistic updates and cache invalidation.
 */
export function useUpdateNotificationPreferences() {
  const queryClient = useQueryClient();

  return $api.useMutation('put', '/api/notification-preferences', {
    onSuccess: () => {
      // Invalidate and refetch notification preferences
      queryClient.invalidateQueries({ queryKey: ['get', '/api/notification-preferences'] });

      // Show success toast
      toast.success('Notification preferences updated successfully');
    },
    onError: () => {
      // Show error toast
      toast.error('Failed to update notification preferences');
    },
  });
}
