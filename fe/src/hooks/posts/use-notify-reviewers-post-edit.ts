import { $api } from '@/lib/api'
import { useTranslations } from '@/lib/i18n'
import { toast } from 'sonner'

/**
 * Hook for notifying reviewers about post edits.
 * 
 * This hook provides a mutation to notify reviewers who have reviewed a post
 * about edits made to the post. Only sends notifications if the post has
 * existing reviews (approved or rework status).
 */
export function useNotifyReviewersPostEdit() {
  const { t, keys } = useTranslations()

  return $api.useMutation('post', '/api/posts/{postId}/notify-reviewers', {
    onSuccess: () => {
      toast.success(t(keys.collaborationHubs.posts.reviewersNotified))
    },
    onError: (error) => {
      console.error('Failed to notify reviewers about post edit:', error)
      toast.error(t(keys.collaborationHubs.posts.failedToNotifyReviewers))
    },
  })
}
