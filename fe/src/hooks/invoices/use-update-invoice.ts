import { $api } from '@/lib/api/client';
import { useQueryClient } from '@tanstack/react-query';

/**
 * Custom hook for updating an existing invoice.
 * Uses openapi-react-query for type-safe API calls with React Query.
 * 
 * Automatically invalidates related queries on success to ensure
 * the UI shows the updated invoice data.
 */
export function useUpdateInvoice() {
  const queryClient = useQueryClient();

  return $api.useMutation('put', '/api/invoices/{id}', {
    onSuccess: (_data, variables) => {
      // Invalidate and refetch all invoices list queries (regardless of parameters)
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/invoices'],
      });

      // Invalidate specific invoice query
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/invoices/{id}', { params: { path: { id: variables.params.path.id } } }],
      });
    },
  });
}
