import { useState, useMemo, useCallback } from 'react'
import { Smile } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { ScrollArea } from '@/components/ui/scroll-area'
import { cn } from '@/lib/utils'
import { useTranslations } from '@/lib/i18n/typed-translations'
import React from 'react'

interface EmojiPickerProps {
  onEmojiSelect: (emoji: string) => void
  disabled?: boolean
  className?: string
  title?: string
}

// Common emoji categories with Unicode emojis
const EMOJI_CATEGORIES = {
  'Smileys & People': [
    '😀', '😃', '😄', '😁', '😆', '😅', '🤣', '😂', '🙂', '🙃',
    '😉', '😊', '😇', '🥰', '😍', '🤩', '😘', '😗', '😚', '😙',
    '😋', '😛', '😜', '🤪', '😝', '🤑', '🤗', '🤭', '🤫', '🤔',
    '🤐', '🤨', '😐', '😑', '😶', '😏', '😒', '🙄', '😬', '🤥',
    '😔', '😪', '🤤', '😴', '😷', '🤒', '🤕', '🤢', '🤮', '🤧',
    '🥵', '🥶', '🥴', '😵', '🤯', '🤠', '🥳', '😎', '🤓', '🧐'
  ],
  'Animals & Nature': [
    '🐶', '🐱', '🐭', '🐹', '🐰', '🦊', '🐻', '🐼', '🐨', '🐯',
    '🦁', '🐮', '🐷', '🐽', '🐸', '🐵', '🙈', '🙉', '🙊', '🐒',
    '🐔', '🐧', '🐦', '🐤', '🐣', '🐥', '🦆', '🦅', '🦉', '🦇',
    '🐺', '🐗', '🐴', '🦄', '🐝', '🐛', '🦋', '🐌', '🐞', '🐜',
    '🌸', '🌺', '🌻', '🌹', '🌷', '🌼', '🌱', '🌿', '🍀', '🌳'
  ],
  'Food & Drink': [
    '🍎', '🍊', '🍋', '🍌', '🍉', '🍇', '🍓', '🍈', '🍒', '🍑',
    '🥭', '🍍', '🥥', '🥝', '🍅', '🍆', '🥑', '🥦', '🥒', '🌶️',
    '🌽', '🥕', '🥔', '🍠', '🥐', '🍞', '🥖', '🥨', '🧀', '🥚',
    '🍳', '🥞', '🧇', '🥓', '🍗', '🍖', '🌭', '🍔', '🍟', '🍕',
    '🥪', '🌮', '🌯', '🥙', '🧆', '🥘', '🍝', '🍜', '🍲', '🍛'
  ],
  'Activities': [
    '⚽', '🏀', '🏈', '⚾', '🥎', '🎾', '🏐', '🏉', '🥏', '🎱',
    '🏓', '🏸', '🏒', '🏑', '🥍', '🏏', '⛳', '🏹', '🎣', '🥊',
    '🥋', '🎽', '⛸️', '🥌', '🛷', '🎿', '⛷️', '🏂', '🏋️', '🤼',
    '🤸', '⛹️', '🤺', '🤾', '🏌️', '🏇', '🧘', '🏄', '🏊', '🤽'
  ],
  'Travel & Places': [
    '🚗', '🚕', '🚙', '🚌', '🚎', '🏎️', '🚓', '🚑', '🚒', '🚐',
    '🛻', '🚚', '🚛', '🚜', '🏍️', '🛵', '🚲', '🛴', '🛹', '🚁',
    '✈️', '🛩️', '🛫', '🛬', '🚀', '🛸', '🚢', '⛵', '🚤', '🛥️',
    '🏠', '🏡', '🏘️', '🏚️', '🏗️', '🏭', '🏢', '🏬', '🏣', '🏤'
  ],
  'Objects': [
    '⌚', '📱', '📲', '💻', '⌨️', '🖥️', '🖨️', '🖱️', '🖲️', '🕹️',
    '🗜️', '💽', '💾', '💿', '📀', '📼', '📷', '📸', '📹', '🎥',
    '📞', '☎️', '📟', '📠', '📺', '📻', '🎙️', '🎚️', '🎛️', '⏰',
    '🕰️', '⏱️', '⏲️', '⏰', '🔔', '🔕', '📢', '📣', '📯', '🔈'
  ],
  'Symbols': [
    '❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍', '🤎', '💔',
    '❣️', '💕', '💞', '💓', '💗', '💖', '💘', '💝', '💟', '☮️',
    '✝️', '☪️', '🕉️', '☸️', '✡️', '🔯', '🕎', '☯️', '☦️', '🛐',
    '⭐', '🌟', '✨', '⚡', '☄️', '💫', '🔥', '💧', '🌊', '❄️'
  ]
}

const EmojiPickerComponent = ({ onEmojiSelect, disabled = false, className, title }: EmojiPickerProps) => {
  const { t, keys } = useTranslations()
  const [open, setOpen] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState('Smileys & People')

  // Memoize category translations to prevent recreation on every render
  const categoryTranslations = useMemo(() => ({
    'Smileys & People': t(keys.ui.emojiPicker.categories.smileysAndPeople),
    'Animals & Nature': t(keys.ui.emojiPicker.categories.animalsAndNature),
    'Food & Drink': t(keys.ui.emojiPicker.categories.foodAndDrink),
    'Activities': t(keys.ui.emojiPicker.categories.activities),
    'Travel & Places': t(keys.ui.emojiPicker.categories.travelAndPlaces),
    'Objects': t(keys.ui.emojiPicker.categories.objects),
    'Symbols': t(keys.ui.emojiPicker.categories.symbols),
  }), [t, keys])

  // Memoize the title to prevent unnecessary re-computations
  const memoizedTitle = useMemo(() =>
    title || t(keys.ui.emojiPicker.addEmoji),
    [title, t, keys]
  )

  const handleEmojiClick = useCallback((emoji: string) => {
    onEmojiSelect(emoji)
    setOpen(false)
  }, [onEmojiSelect])

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger>
        <Button
          type="button"
          variant="ghost"
          size="sm"
          className={cn("h-8 w-8 p-0 shrink-0 hover:bg-muted", className)}
          disabled={disabled}
          title={memoizedTitle}
        >
          <Smile className="h-4 w-4" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0" align="start">
        <div className="flex flex-col">
          {/* Category tabs */}
          <div className="flex border-b">
            <ScrollArea className="w-full">
              <div className="flex p-1">
                {Object.keys(EMOJI_CATEGORIES).map((category) => (
                  <Button
                    key={category}
                    type="button"
                    variant={selectedCategory === category ? "secondary" : "ghost"}
                    size="sm"
                    className="text-xs whitespace-nowrap"
                    onClick={() => setSelectedCategory(category)}
                  >
                    {categoryTranslations[category as keyof typeof categoryTranslations]?.split(' ')[0] || category.split(' ')[0]}
                  </Button>
                ))}
              </div>
            </ScrollArea>
          </div>

          {/* Emoji grid */}
          <ScrollArea className="h-64">
            <div className="grid grid-cols-8 gap-1 p-2">
              {EMOJI_CATEGORIES[selectedCategory as keyof typeof EMOJI_CATEGORIES].map((emoji, index) => (
                <Button
                  key={`${emoji}-${index}`}
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 text-lg hover:bg-muted"
                  onClick={() => handleEmojiClick(emoji)}
                >
                  {emoji}
                </Button>
              ))}
            </div>
          </ScrollArea>
        </div>
      </PopoverContent>
    </Popover>
  )
}

// Memoize the component to prevent unnecessary re-renders
export const EmojiPicker = React.memo(EmojiPickerComponent)
