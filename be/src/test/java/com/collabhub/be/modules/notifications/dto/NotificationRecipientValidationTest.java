package com.collabhub.be.modules.notifications.dto;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class to verify that the validation works correctly with UnifiedNotificationRecipient.
 *
 * This test specifically addresses the issue where <PERSON><PERSON><PERSON> Val<PERSON><PERSON> was trying to
 * validate the getUserId() method on external user recipients, causing an
 * UnsupportedOperationException during chat mention notification processing.
 */
class NotificationRecipientValidationTest {

    private Validator validator;

    @BeforeEach
    void setUp() {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        validator = factory.getValidator();
    }

    @Test
    @DisplayName("UnifiedNotificationRecipient (external) should validate successfully without calling getUserId()")
    void testExternalUnifiedRecipientValidation() {
        // Given: A valid external user recipient
        UnifiedNotificationRecipient externalRecipient = UnifiedNotificationRecipient.forExternalUser(
            "<EMAIL>",
            "Jane Smith"
        );

        // When: Validating the external recipient
        Set<ConstraintViolation<UnifiedNotificationRecipient>> violations = validator.validate(externalRecipient);

        // Then: No validation violations should occur
        assertTrue(violations.isEmpty(),
            "External UnifiedNotificationRecipient should validate without errors, but got: " + violations);
    }

    @Test
    @DisplayName("UnifiedNotificationRecipient (internal) should validate successfully with getUserId()")
    void testInternalUnifiedRecipientValidation() {
        // Given: A valid internal user recipient
        UnifiedNotificationRecipient internalRecipient = UnifiedNotificationRecipient.forInternalUser(
            123L,
            "<EMAIL>",
            "John Doe"
        );

        // When: Validating the internal recipient
        Set<ConstraintViolation<UnifiedNotificationRecipient>> violations = validator.validate(internalRecipient);

        // Then: No validation violations should occur
        assertTrue(violations.isEmpty(),
            "Internal UnifiedNotificationRecipient should validate without errors, but got: " + violations);
    }

    @Test
    @DisplayName("Mixed recipient list should validate successfully")
    void testMixedRecipientListValidation() {
        // Given: A mixed list of internal and external recipients
        List<NotificationRecipient> recipients = List.of(
            UnifiedNotificationRecipient.forInternalUser(123L, "<EMAIL>", "John Doe"),
            UnifiedNotificationRecipient.forExternalUser("<EMAIL>", "Jane Smith"),
            UnifiedNotificationRecipient.forInternalUser(456L, "<EMAIL>", "Alice Johnson"),
            UnifiedNotificationRecipient.forExternalUser("<EMAIL>", "Bob Wilson")
        );

        // When: Validating each recipient in the list
        // This simulates what happens during ChatMentionNotification validation
        assertDoesNotThrow(() -> {
            for (NotificationRecipient recipient : recipients) {
                Set<ConstraintViolation<NotificationRecipient>> violations = validator.validate(recipient);
                assertTrue(violations.isEmpty(), 
                    "Recipient " + recipient.getClass().getSimpleName() + " should validate without errors, but got: " + violations);
            }
        }, "Mixed recipient list validation should not throw any exceptions");
    }

    @Test
    @DisplayName("External UnifiedNotificationRecipient getUserId() should throw UnsupportedOperationException")
    void testExternalUnifiedRecipientGetUserIdThrowsException() {
        // Given: A valid external user recipient
        UnifiedNotificationRecipient externalRecipient = UnifiedNotificationRecipient.forExternalUser(
            "<EMAIL>",
            "Jane Smith"
        );

        // When & Then: Calling getUserId() should throw UnsupportedOperationException
        assertThrows(UnsupportedOperationException.class,
            externalRecipient::userId,
            "External UnifiedNotificationRecipient.getUserId() should throw UnsupportedOperationException");
    }

    @Test
    @DisplayName("Internal UnifiedNotificationRecipient getUserId() should return valid user ID")
    void testInternalUnifiedRecipientGetUserIdReturnsValidId() {
        // Given: A valid internal user recipient
        Long expectedUserId = 123L;
        UnifiedNotificationRecipient internalRecipient = UnifiedNotificationRecipient.forInternalUser(
            expectedUserId,
            "<EMAIL>",
            "John Doe"
        );

        // When: Calling getUserId()
        Long actualUserId = internalRecipient.userId();

        // Then: Should return the expected user ID
        assertEquals(expectedUserId, actualUserId,
            "Internal UnifiedNotificationRecipient.getUserId() should return the correct user ID");
    }

    @Test
    @DisplayName("ChatMentionNotification with mixed recipients should validate successfully")
    void testChatMentionNotificationValidation() {
        // Given: A ChatMentionNotification with mixed recipients
        List<NotificationRecipient> recipients = List.of(
            UnifiedNotificationRecipient.forInternalUser(123L, "<EMAIL>", "John Doe"),
            UnifiedNotificationRecipient.forExternalUser("<EMAIL>", "Jane Smith")
        );

        // When: Creating and validating a ChatMentionNotification
        assertDoesNotThrow(() -> {
            ChatMentionNotification notification = ChatMentionNotification.builder()
                .recipients(recipients)
                .mentionerName("Alice Johnson")
                .channelName("General Discussion")
                .messagePreview("Hey everyone, what do you think about this?")
                .entityIds(1L, 2L)
                .build();

            // This should not throw any validation exceptions
            notification.validate();
        }, "ChatMentionNotification with mixed recipients should validate without exceptions");
    }
}
