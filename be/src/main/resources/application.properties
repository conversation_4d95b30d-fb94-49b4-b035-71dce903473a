spring.application.name=be

# Database Configuration
spring.datasource.url=*****************************************
spring.datasource.username=postgres
spring.datasource.password=postgres
spring.datasource.driver-class-name=org.postgresql.Driver

# Flyway Configuration
spring.flyway.enabled=true
spring.flyway.locations=classpath:db/migration
spring.flyway.baseline-on-migrate=true

# jOOQ Configuration
spring.jooq.sql-dialect=postgres

# Authentication Configuration
app.auth.jwt.issuer=https://api.collabhub.com
app.auth.jwt.audience=collabhub-api
app.auth.jwt.access-token-ttl=PT15M
app.auth.jwt.signature-algorithm=RS256
app.auth.jwt.clock-skew=PT2M

app.auth.refresh-token.ttl=P30D
app.auth.refresh-token.absolute-lifetime=P90D
app.auth.refresh-token.rotation-enabled=true
app.auth.refresh-token.token-length=64

# Cookie Configuration
app.auth.cookie.refresh-token-name=refresh_token
app.auth.cookie.path=/
app.auth.cookie.same-site=Lax
app.auth.cookie.http-only=true
app.auth.cookie.secure=false

# Spring Security Configuration
spring.security.oauth2.authorizationserver.issuer=https://api.collabhub.com

# Email Configuration (Mailhog for development)
spring.mail.host=localhost
spring.mail.port=1025
spring.mail.username=
spring.mail.password=
spring.mail.properties.mail.smtp.auth=false
spring.mail.properties.mail.smtp.starttls.enable=false

# Application Configuration
app.base-url=http://localhost:8080
app.frontend-url=http://localhost:5173

# Verification Token Configuration
app.auth.verification-token.email-verification-ttl=PT24H

# S3 Configuration (MinIO for development)
app.s3.endpoint=http://localhost:9000
app.s3.access-key=minioadmin
app.s3.secret-key=minioadmin
app.s3.region=us-east-1
app.s3.bucket-name=collabhub
app.s3.max-file-size=100MB
app.s3.allowed-image-types=image/jpeg,image/png,image/gif,image/webp
app.s3.allowed-video-types=video/mp4,video/quicktime,video/x-msvideo
app.s3.max-image-size-bytes=10485760
app.s3.max-video-size-bytes=104857600
app.s3.allowed-extensions=.jpg,.jpeg,.png,.gif,.webp,.mp4,.mov,.avi
app.s3.blocked-extensions=.exe,.bat,.cmd,.com,.pif,.scr,.vbs,.js,.jar,.php,.asp,.jsp,.sh,.ps1,.py,.rb

# AWS S3 Configuration (for production)
# aws.region=${AWS_REGION:us-east-1}
# aws.s3.bucket-name=${S3_BUCKET_NAME:collabhub}
# aws.s3.max-file-size=${S3_MAX_FILE_SIZE:100MB}

# CORS Configuration
app.cors.allowed-origins=http://localhost:5173,http://localhost:3000

# Resilience4j Rate Limiter Configuration
resilience4j.ratelimiter.instances.s3-upload.limit-for-period=60
resilience4j.ratelimiter.instances.s3-upload.limit-refresh-period=1m
resilience4j.ratelimiter.instances.s3-upload.timeout-duration=1s

resilience4j.ratelimiter.instances.s3-download.limit-for-period=300
resilience4j.ratelimiter.instances.s3-download.limit-refresh-period=1m
resilience4j.ratelimiter.instances.s3-download.timeout-duration=1s

# Notification Batching Configuration
app.notifications.batching.enabled=true
app.notifications.batching.process-interval-seconds=180
app.notifications.batching.max-retry-attempts=3
app.notifications.batching.retry-delay-minutes=5
app.notifications.batching.cleanup-retention-hours=24
app.notifications.batching.lock-timeout-minutes=10

resilience4j.ratelimiter.instances.s3-batch-upload.limit-for-period=60
resilience4j.ratelimiter.instances.s3-batch-upload.limit-refresh-period=1m
resilience4j.ratelimiter.instances.s3-batch-upload.timeout-duration=5s

# Logging Configuration
logging.level.com.collabhub.be=DEBUG
logging.level.org.springframework.security=DEBUG
logging.level.org.springframework.security.oauth2=DEBUG
