-- Migration V032: Add POST_EDITED notification type
-- Adds POST_EDITED to the notification_type_enum to support notifications when reviewed posts are edited

-- Add POST_EDITED to the notification_type_enum
ALTER TYPE notification_type_enum ADD VALUE 'POST_EDITED';

-- Update table comment to document the new notification type
COMMENT ON TYPE notification_type_enum IS 'Types of notifications: INVITE_TO_HUB, ASSIGNED_AS_REVIEWER, POST_REVIEWED, POST_EDITED, COMMENT_ADDED, COMMENT_MENTION, CHAT_MENTION, CHAT_ADDED, BRIEF_CREATED, BRIEF_UPDATED, BRIEF_ASSIGNED';
