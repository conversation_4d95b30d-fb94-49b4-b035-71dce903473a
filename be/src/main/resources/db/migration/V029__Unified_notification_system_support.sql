-- Migration V029: Unified notification system support for internal and external users
-- This migration enables the notification system to handle both:
-- - Internal users: identified by user_id (references user table)
-- - External users: identified by email only (hub participants)

-- ========================================
-- STEP 1: Update notification table
-- ========================================

-- Add email column for external user support
ALTER TABLE notification
ADD COLUMN email VARCHAR(255);

-- Make user_id nullable to support external users
ALTER TABLE notification
ALTER COLUMN user_id DROP NOT NULL;

-- ========================================
-- STEP 2: Update notification_batch_queue table
-- ========================================

-- Add email column for external user support
ALTER TABLE notification_batch_queue
ADD COLUMN email VARCHAR(255);

-- Make user_id nullable to support external users
ALTER TABLE notification_batch_queue
ALTER COLUMN user_id DROP NOT NULL;

-- ========================================
-- STEP 3: Create simple performance indexes
-- ========================================

-- Simple indexes for notification queries
CREATE INDEX idx_notification_email ON notification(email);

-- Simple indexes for batch queue queries
CREATE INDEX idx_batch_queue_email ON notification_batch_queue(email);
CREATE INDEX idx_batch_queue_user_id ON notification_batch_queue(user_id);

-- ========================================
-- STEP 4: Update table comments
-- ========================================

COMMENT ON COLUMN notification.user_id IS 'User ID for internal users (nullable for external users)';
COMMENT ON COLUMN notification.email IS 'Email address for external users (nullable for internal users)';

COMMENT ON COLUMN notification_batch_queue.user_id IS 'User ID for internal users (nullable for external users)';
COMMENT ON COLUMN notification_batch_queue.email IS 'Email address for external users (nullable for internal users)';

-- Update table comments to reflect unified support
COMMENT ON TABLE notification IS 'Unified notification storage supporting both internal users (user_id) and external users (email)';
COMMENT ON TABLE notification_batch_queue IS 'Unified notification batching queue supporting both internal users (user_id) and external users (email)';

