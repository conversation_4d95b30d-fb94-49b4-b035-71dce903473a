package com.collabhub.be.modules.posts.controller;

import com.collabhub.be.modules.auth.dto.UserContext;
import com.collabhub.be.modules.auth.service.JwtClaimsService;
import com.collabhub.be.modules.invoice.dto.PageRequest;
import com.collabhub.be.modules.posts.constants.PostConstants;
import com.collabhub.be.modules.posts.dto.*;
import com.collabhub.be.modules.posts.service.PostReviewService;
import com.collabhub.be.modules.posts.service.PostService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.jooq.generated.enums.ReviewStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;



/**
 * REST controller for managing posts in collaboration hubs.
 * Provides endpoints for CRUD operations, media upload, and filtering.
 */
@RestController
@RequestMapping("/api")
@Tag(name = "Posts", description = "Post management in collaboration hubs")
public class PostController {

    private static final Logger logger = LoggerFactory.getLogger(PostController.class);

    private final PostService postService;
    private final PostReviewService postReviewService;
    private final JwtClaimsService jwtClaimsService;

    public PostController(PostService postService, PostReviewService postReviewService,
                         JwtClaimsService jwtClaimsService) {
        this.postService = postService;
        this.postReviewService = postReviewService;
        this.jwtClaimsService = jwtClaimsService;
    }

    @Operation(
        summary = "Create a new post",
        description = "Creates a new post in a collaboration hub. Post must have either caption or media content (or both). " +
                     "Only content creators, admins, and reviewer-creators can create posts.",
        responses = {
            @ApiResponse(responseCode = "201", description = "Post created successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid request data or validation errors"),
            @ApiResponse(responseCode = "403", description = "User lacks permission to create posts in this hub"),
            @ApiResponse(responseCode = "404", description = "Hub not found")
        }
    )
    @PostMapping("/hubs/{hubId}/posts")
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).POST_WRITE.permission)")
    public ResponseEntity<PostResponse> createPost(
            @Parameter(description = "Hub ID") @PathVariable Long hubId,
            @Valid @RequestBody PostCreateRequest request,
            @AuthenticationPrincipal Jwt jwt) {

        logger.debug("Creating post in hub {}", hubId);

        PostResponse response = postService.createPost(hubId, request);

        logger.debug("Successfully created post {} in hub {}", response.getId(), hubId);
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    @Operation(summary = "List posts", description = "Gets a paginated list of posts with filtering")
    @GetMapping("/hubs/{hubId}/posts")
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).POST_READ.permission)")
    public ResponseEntity<PostListResponse> getPosts(
            @Parameter(description = "Hub ID") @PathVariable Long hubId,
            @Parameter(description = "Post filter type") @RequestParam(value = "filter", defaultValue = PostConstants.DEFAULT_FILTER) String filterValue,
            @Parameter(description = "Review status filter") @RequestParam(value = "status", required = false) ReviewStatus status,
            @Parameter(description = "Page number (0-based)") @RequestParam(value = "page", defaultValue = "0") int page,
            @Parameter(description = "Page size (max 100)") @RequestParam(value = "size", defaultValue = "20") int size,
            @AuthenticationPrincipal Jwt jwt) {

        logger.debug("Retrieving posts for hub {} with filter: {}, status: {}", hubId, filterValue, status);

        // Convert string filter to enum with fallback to ALL
        PostFilter filter = PostFilter.fromString(filterValue);
        PageRequest pageRequest = new PageRequest(page, size);
        PostListResponse response = postService.getPosts(hubId, pageRequest, filter, status);

        logger.debug("Retrieved {} posts for hub {}", response.getContent().size(), hubId);
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Get post details", description = "Gets detailed information about a specific post")
    @GetMapping("/posts/{postId}")
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).POST_READ.permission)")
    public ResponseEntity<PostResponse> getPost(
            @Parameter(description = "Post ID") @PathVariable Long postId,
            @AuthenticationPrincipal Jwt jwt) {

        logger.debug("Retrieving post details for post {}", postId);

        PostResponse response = postService.getPostDetails(postId);

        logger.debug("Successfully retrieved post details for post {}", postId);
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Update a post", description = "Updates an existing post")
    @PutMapping("/posts/{postId}")
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).POST_UPDATE.permission)")
    public ResponseEntity<PostResponse> updatePost(
            @Parameter(description = "Post ID") @PathVariable Long postId,
            @Valid @RequestBody PostUpdateRequest request,
            @AuthenticationPrincipal Jwt jwt) {

        logger.debug("Updating post {}", postId);

        PostResponse response = postService.updatePost(postId, request);

        logger.debug("Successfully updated post {}", postId);
        return ResponseEntity.ok(response);
    }

    @Operation(
        summary = "Delete a post",
        description = "Soft deletes a post and removes associated media files from S3. " +
                     "Only post creators and hub admins can delete posts.",
        responses = {
            @ApiResponse(responseCode = "204", description = "Post deleted successfully"),
            @ApiResponse(responseCode = "403", description = "User lacks permission to delete this post"),
            @ApiResponse(responseCode = "404", description = "Post not found")
        }
    )
    @DeleteMapping("/posts/{postId}")
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).POST_DELETE.permission)")
    public ResponseEntity<Void> deletePost(
            @Parameter(description = "Post ID") @PathVariable Long postId,
            @AuthenticationPrincipal Jwt jwt) {

        logger.debug("Deleting post {}", postId);

        postService.deletePost(postId);

        logger.debug("Successfully deleted post {}", postId);
        return ResponseEntity.noContent().build();
    }

    @Operation(summary = "Upload media file", description = "Uploads a media file (image or video) for posts")
    @PostMapping(value = "/posts/media/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).POST_WRITE.permission)")
    public ResponseEntity<FileUploadResponse> uploadMedia(
            @Parameter(description = "Media file to upload") @RequestParam("file") MultipartFile file,
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.debug("Uploading media file: {} for account {}", file.getOriginalFilename(), userContext.getAccountId());

        FileUploadResponse response = postService.uploadMedia(file, userContext.getAccountId());

        logger.debug("Successfully uploaded media file: {}", response.getUrl());
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    @Operation(
        summary = "Submit or update a post review",
        description = "Submits or updates a review for a post. Only assigned reviewers and hub admins can review posts. " +
                     "Uses upsert behavior - one review per reviewer per post.",
        responses = {
            @ApiResponse(responseCode = "200", description = "Review submitted successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid request data or validation errors"),
            @ApiResponse(responseCode = "403", description = "User lacks permission to review this post"),
            @ApiResponse(responseCode = "404", description = "Post not found")
        }
    )
    @PostMapping("/posts/{postId}/reviews")
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).CONTENT_REVIEW.permission)")
    public ResponseEntity<PostReviewResponse> submitReview(
            @Parameter(description = "Post ID") @PathVariable Long postId,
            @Valid @RequestBody PostReviewRequest request,
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.debug("Submitting review for post {} by user {}", postId, userContext.getUserId());

        PostReviewResponse response = postReviewService.submitReview(postId, request, userContext);

        logger.debug("Successfully submitted review for post {} by user {}", postId, userContext.getUserId());
        return ResponseEntity.ok(response);
    }

    @Operation(
        summary = "Notify reviewers about post edits",
        description = "Notifies reviewers who have reviewed a post about edits made to the post. " +
                     "Only sends notifications if the post has existing reviews (approved or rework status). " +
                     "Only post creators and hub admins can trigger these notifications.",
        responses = {
            @ApiResponse(responseCode = "200", description = "Notifications sent successfully"),
            @ApiResponse(responseCode = "403", description = "User lacks permission to notify reviewers for this post"),
            @ApiResponse(responseCode = "404", description = "Post not found")
        }
    )
    @PostMapping("/posts/{postId}/notify-reviewers")
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).POST_UPDATE.permission)")
    public ResponseEntity<Void> notifyReviewersAboutPostEdit(
            @Parameter(description = "Post ID") @PathVariable Long postId,
            @AuthenticationPrincipal Jwt jwt) {

        logger.debug("Notifying reviewers about post edit for post {}", postId);

        postReviewService.notifyReviewersAboutPostEdit(postId);

        logger.debug("Successfully processed reviewer notifications for post {}", postId);
        return ResponseEntity.ok().build();
    }
}
