package com.collabhub.be.modules.notifications.service;

import com.collabhub.be.modules.notifications.dto.BaseNotification;
import com.collabhub.be.modules.notifications.dto.NotificationChannel;
import com.collabhub.be.modules.notifications.dto.NotificationMetadata;
import com.collabhub.be.modules.notifications.dto.NotificationRecipient;
import com.collabhub.be.modules.notifications.dto.NotificationType;
import com.collabhub.be.modules.notifications.dto.NotificationUrgency;
import com.collabhub.be.modules.notifications.exception.NotificationBatchingException;
import com.collabhub.be.modules.notifications.util.NotificationRecipientUtils;
import com.collabhub.be.modules.notifications.repository.NotificationPreferenceRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;

/**
 * Production-grade notification dispatcher service that coordinates notification delivery.
 *
 * <p>This service provides the main entry point for dispatching notifications throughout
 * the application, handling routing between in-app and email channels based on user types,
 * preferences, and notification urgency levels. It uses strongly-typed data structures
 * and comprehensive error handling for production reliability.</p>
 *
 * <h3>Key Features:</h3>
 * <ul>
 *   <li>Strongly-typed metadata using {@link NotificationMetadata} for type safety</li>
 *   <li>Urgency-based routing with {@link NotificationUrgency} for batching control</li>
 *   <li>Automatic user type detection and channel routing</li>
 *   <li>Comprehensive logging and error handling</li>
 *   <li>Production-grade validation and null safety</li>
 * </ul>
 *
 * <h3>Usage Examples:</h3>
 * <pre>
 * // Dispatch with strongly-typed metadata
 * NotificationMetadata metadata = NotificationMetadata.builder()
 *     .actorName("John Doe")
 *     .targetTitle("Summer Campaign")
 *     .actionContext("mentioned you in a comment")
 *     .deepLinkPath("/app/posts/123#comment-456")
 *     .build();
 *
 * dispatcher.dispatchNotification(
 *     NotificationType.COMMENT_MENTION,
 *     "You were mentioned",
 *     "John Doe mentioned you in a comment",
 *     List.of(userId),
 *     entityReferences,
 *     metadata,
 *     NotificationUrgency.HIGH
 * );
 * </pre>
 *
 * <AUTHOR> Hub Team
 * @since 1.0.0
 */
@Service
@Validated
public class NotificationDispatcherService {

    private static final Logger logger = LoggerFactory.getLogger(NotificationDispatcherService.class);

    // Constants
    private static final int MAX_TITLE_LENGTH = 255;
    private static final int MAX_MESSAGE_LENGTH = 1000;

    private final NotificationStorageService notificationStorageService;
    private final NotificationBatchingService notificationBatchingService;
    private final NotificationPreferenceService notificationPreferenceService;
    private final NotificationPreferenceRepository notificationPreferenceRepository;
    private final NotificationTranslationService notificationTranslationService;

    public NotificationDispatcherService(NotificationStorageService notificationStorageService,
                                       NotificationBatchingService notificationBatchingService,
                                       NotificationPreferenceService notificationPreferenceService,
                                       NotificationPreferenceRepository notificationPreferenceRepository,
                                       NotificationTranslationService notificationTranslationService) {
        this.notificationStorageService = notificationStorageService;
        this.notificationBatchingService = notificationBatchingService;
        this.notificationPreferenceService = notificationPreferenceService;
        this.notificationPreferenceRepository = notificationPreferenceRepository;
        this.notificationTranslationService = notificationTranslationService;
    }

    /**
     * Dispatches notifications to mixed recipients with full urgency control.
     *
     * <p>This method provides complete control over notification dispatch for mixed
     * recipient types, including urgency levels and comprehensive validation.</p>
     *
     * @param type the notification type (must not be null)
     * @param title the notification title (max 255 characters)
     * @param message the notification message (max 1000 characters)
     * @param recipients the mixed recipient list (must not be empty)
     * @param entityReferences optional entity references for deep linking
     * @param metadata optional strongly-typed metadata providing additional context
     * @param urgency notification urgency level (must not be null)
     *
     * @throws IllegalArgumentException if required parameters are invalid
     * @throws NotificationBatchingException if dispatch processing fails
     */
    @Transactional
    protected void dispatchNotification(@NotNull @Valid NotificationType type,
                                        @NotBlank @Size(max = MAX_TITLE_LENGTH) String title,
                                        @NotBlank @Size(max = MAX_MESSAGE_LENGTH) String message,
                                        @NotEmpty List<NotificationRecipient> recipients,
                                        NotificationStorageService.EntityReferences entityReferences,
                                        @Valid NotificationMetadata metadata,
                                        @NotNull @Valid NotificationUrgency urgency) {

        validateMixedDispatchParameters(title, message, recipients);
        processUnifiedNotifications(recipients, type, title, message, entityReferences, metadata, urgency);
    }

    /**
     * Dispatches a strongly-typed notification object.
     *
     * <p>This method provides a type-safe way to dispatch notifications using strongly-typed
     * notification objects instead of generic parameters. The notification object encapsulates
     * all required data and automatically generates titles, messages, and metadata.</p>
     *
     * <h3>Usage Example:</h3>
     * <pre>
     * CommentMentionNotification notification = CommentMentionNotification.builder()
     *     .recipients(mentionedParticipants)
     *     .commenterName("John Doe")
     *     .postTitle("Summer Campaign")
     *     .commentPreview("Great work on this...")
     *     .entityIds(123L, 456L, 789L)
     *     .build();
     *
     * notificationDispatcher.dispatch(notification);
     * </pre>
     *
     * @param notification the strongly-typed notification object (must not be null)
     * @throws IllegalArgumentException if notification is invalid
     * @throws NotificationBatchingException if dispatch processing fails
     */
    @Transactional
    public void dispatch(@NotNull @Valid BaseNotification notification) {
        logger.debug("Dispatching strongly-typed notification: type={}, recipients={}, urgency={}",
                    notification.getType(), notification.getRecipients().size(), notification.getUrgency());

        try {
            // Validate the notification
            notification.validate();

            // Generate title and message using the notification's built-in logic
            String title = notification.generateTitle(notificationTranslationService);
            String message = notification.generateMessage(notificationTranslationService);
            NotificationMetadata metadata = notification.generateMetadata();

            // Dispatch using the existing mixed notification infrastructure
            dispatchNotification(
                notification.getType(),
                title,
                message,
                notification.getRecipients(),
                notification.getEntityReferences(),
                metadata,
                notification.getUrgency()
            );

            logger.info("Successfully dispatched strongly-typed notification: type={}, recipients={}, urgency={}",
                       notification.getType(), notification.getRecipients().size(), notification.getUrgency());

        } catch (Exception e) {
            logger.error("Failed to dispatch strongly-typed notification: type={}, error={}",
                        notification.getType(), e.getMessage(), e);
            throw new NotificationBatchingException("Failed to dispatch strongly-typed notification", e);
        }
    }

    /**
     * Validates mixed dispatch parameters for consistency and business rules.
     */
    private void validateMixedDispatchParameters(@NotBlank String title,
                                                 @NotBlank String message,
                                                 @NotEmpty List<NotificationRecipient> recipients) {

        // Basic validation
        if (title.length() > MAX_TITLE_LENGTH) {
            throw new IllegalArgumentException("Title exceeds maximum length of " + MAX_TITLE_LENGTH + " characters");
        }

        if (message.length() > MAX_MESSAGE_LENGTH) {
            throw new IllegalArgumentException("Message exceeds maximum length of " + MAX_MESSAGE_LENGTH + " characters");
        }

        if (recipients.isEmpty()) {
            throw new IllegalArgumentException("Recipients list cannot be empty");
        }

        // Validate all recipients
        NotificationRecipientUtils.validateRecipients(recipients);
    }

    /**
     * Processes notifications for unified recipients (both internal and external users).
     */
    private void processUnifiedNotifications(List<NotificationRecipient> recipients,
                                            NotificationType type, String title, String message,
                                            NotificationStorageService.EntityReferences entityReferences,
                                            NotificationMetadata metadata, NotificationUrgency urgency) {

        if (recipients.isEmpty()) {
            return;
        }

        logger.debug("Processing notifications for {} unified recipients", recipients.size());

        // Create unified delivery plan based on preferences
        UnifiedDeliveryPlan deliveryPlan = createUnifiedDeliveryPlan(type, recipients);

        // Process in-app notifications (mandatory for external users, preference-based for internal users)
        processInAppNotifications(deliveryPlan, type, title, message, entityReferences, metadata);

        // Process email notifications (preference-based for both user types)
        processEmailNotifications(deliveryPlan, type, title, message, entityReferences, metadata, urgency);

        logger.info("Processed notifications for {} recipients (in-app: {}, email: {})",
                   recipients.size(), deliveryPlan.getInAppRecipients().size(), deliveryPlan.getEmailRecipients().size());
    }

    /**
     * Creates a unified delivery plan for all recipient types based on preferences.
     */
    private UnifiedDeliveryPlan createUnifiedDeliveryPlan(NotificationType type,
                                                          List<NotificationRecipient> recipients) {
        UnifiedDeliveryPlan deliveryPlan = new UnifiedDeliveryPlan();

        for (NotificationRecipient recipient : recipients) {
            // In-app notifications
            if (recipient.isInternal()) {
                // Internal users: check preferences
                Boolean inAppEnabled = notificationPreferenceRepository.isNotificationEnabled(
                    recipient.userId(), type, NotificationChannel.IN_APP);
                if (shouldDeliverNotification(inAppEnabled)) {
                    deliveryPlan.addInAppRecipient(recipient);
                }
            } else {
                // External users: always receive in-app notifications
                deliveryPlan.addInAppRecipient(recipient);
            }

            // Email notifications: check preferences for both types
            Boolean emailEnabled;
            if (recipient.isInternal()) {
                emailEnabled = notificationPreferenceRepository.isNotificationEnabled(
                    recipient.userId(), type, NotificationChannel.EMAIL);
            } else {
                emailEnabled = notificationPreferenceService.isExternalNotificationEnabled(
                    recipient.email(), type, NotificationChannel.EMAIL);
            }

            if (shouldDeliverNotification(emailEnabled)) {
                deliveryPlan.addEmailRecipient(recipient);
            }
        }

        return deliveryPlan;
    }

    /**
     * Processes in-app notifications for unified recipients.
     */
    private void processInAppNotifications(UnifiedDeliveryPlan deliveryPlan,
                                          NotificationType type, String title, String message,
                                          NotificationStorageService.EntityReferences entityReferences,
                                          NotificationMetadata metadata) {

        if (!deliveryPlan.hasInAppRecipients()) {
            return;
        }

        List<NotificationRecipient> inAppRecipients = deliveryPlan.getInAppRecipients();

        // Create in-app notifications using the unified storage service
        List<NotificationRecipient> recipients = new ArrayList<>(inAppRecipients);
        notificationStorageService.createMixedNotifications(type, title, message, recipients, entityReferences, metadata);

        logger.info("Created {} in-app notifications", inAppRecipients.size());
    }

    /**
     * Processes email notifications for unified recipients.
     */
    private void processEmailNotifications(UnifiedDeliveryPlan deliveryPlan,
                                          NotificationType type, String title, String message,
                                          NotificationStorageService.EntityReferences entityReferences,
                                          NotificationMetadata metadata, NotificationUrgency urgency) {

        if (!deliveryPlan.hasEmailRecipients()) {
            return;
        }

        List<NotificationRecipient> emailRecipients = deliveryPlan.getEmailRecipients();

        // All notifications go through unified batching
        notificationBatchingService.queueUnifiedNotificationsForBatching(
            type, title, message, emailRecipients, entityReferences, metadata, urgency);

        logger.info("Queued {} email notifications for batching", emailRecipients.size());
    }

    /**
     * Determines if a notification should be delivered based on preference value.
     */
    private boolean shouldDeliverNotification(Boolean enabled) {
        // If preference is null (not set), default to enabled
        // If preference is explicitly set, use that value
        return enabled == null || enabled;
    }

    /**
     * Data class for unified delivery planning.
     */
    private static class UnifiedDeliveryPlan {
        private final List<NotificationRecipient> inAppRecipients = new ArrayList<>();
        private final List<NotificationRecipient> emailRecipients = new ArrayList<>();

        public void addInAppRecipient(NotificationRecipient recipient) {
            inAppRecipients.add(recipient);
        }

        public void addEmailRecipient(NotificationRecipient recipient) {
            emailRecipients.add(recipient);
        }

        public List<NotificationRecipient> getInAppRecipients() {
            return inAppRecipients;
        }

        public List<NotificationRecipient> getEmailRecipients() {
            return emailRecipients;
        }

        public boolean hasInAppRecipients() {
            return !inAppRecipients.isEmpty();
        }

        public boolean hasEmailRecipients() {
            return !emailRecipients.isEmpty();
        }
    }

}
