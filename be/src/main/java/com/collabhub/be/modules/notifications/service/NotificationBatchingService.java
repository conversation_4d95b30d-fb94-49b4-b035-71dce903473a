package com.collabhub.be.modules.notifications.service;


import com.collabhub.be.modules.notifications.dto.*;
import com.collabhub.be.modules.notifications.exception.NotificationBatchingException;
import com.collabhub.be.modules.notifications.repository.NotificationBatchQueueRepository;
import com.collabhub.be.util.JsonbUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.jooq.JSONB;
import org.jooq.generated.tables.pojos.NotificationBatchQueue;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Production-grade service for managing notification batching operations.
 *
 * <p>This service handles queuing notifications for batching and determining delivery
 * timing based on urgency levels and batching configuration. It uses strongly-typed
 * data structures and provides comprehensive validation and error handling.</p>
 *
 * <h3>Key Features:</h3>
 * <ul>
 *   <li>Strongly-typed metadata using {@link NotificationMetadata} with ObjectMapper serialization</li>
 *   <li>Urgency-based batching with {@link NotificationUrgency}</li>
 *   <li>Unified recipient handling for both internal and external users</li>
 *   <li>Production-grade validation and null safety</li>
 *   <li>Comprehensive logging and monitoring support</li>
 * </ul>
 *
 * <p>Note: This service only queues notifications for batching. The actual batch processing,
 * configuration, and timing logic is handled by {@link NotificationBatchProcessor}.</p>
 *
 * <AUTHOR> Hub Team
 * @since 1.0.0
 */
@Service
@Validated
public class NotificationBatchingService {

    private static final Logger logger = LoggerFactory.getLogger(NotificationBatchingService.class);

    // Constants
    private static final int MAX_TITLE_LENGTH = 255;
    private static final int MAX_MESSAGE_LENGTH = 1000;
    private static final String QUEUE_DEBUG_MESSAGE = "Queuing {} notifications of type {} with urgency {} for batching";
    private static final String QUEUE_SINGLE_DEBUG_MESSAGE = "Queued notification for user {} in batch window {}";

    private final NotificationBatchQueueRepository batchQueueRepository;
    private final ObjectMapper objectMapper;

    public NotificationBatchingService(NotificationBatchQueueRepository batchQueueRepository,
                                     ObjectMapper objectMapper) {
        this.batchQueueRepository = batchQueueRepository;
        this.objectMapper = objectMapper;
    }


    /**
     * Sets entity references on the queue entry if provided.
     */
    private void setEntityReferences(NotificationBatchQueue queueEntry,
                                   NotificationStorageService.EntityReferences entityReferences) {
        if (entityReferences != null) {
            queueEntry.setCollaborationHubId(entityReferences.getHubId());
            queueEntry.setPostId(entityReferences.getPostId());
            queueEntry.setCommentId(entityReferences.getCommentId());
            queueEntry.setChatChannelId(entityReferences.getChatChannelId());
            queueEntry.setBriefId(entityReferences.getBriefId());
        }
    }

    /**
     * Sets metadata on the queue entry if provided.
     */
    private void setMetadata(NotificationBatchQueue queueEntry, NotificationMetadata metadata) {
        if (metadata != null && metadata.hasContent()) {
            queueEntry.setMetadata(convertMetadataToJsonb(metadata));
        }
    }

    /**
     * Converts strongly-typed NotificationMetadata to JSONB format for database storage.
     * Uses ObjectMapper for proper JSON serialization with null safety and error handling.
     *
     * @param metadata the strongly-typed metadata to convert
     * @return JSONB representation suitable for database storage
     */
    private JSONB convertMetadataToJsonb(NotificationMetadata metadata) {
        if (metadata == null || !metadata.hasContent()) {
            return null;
        }

        try {
            return JsonbUtil.toJsonb(metadata, objectMapper);
        } catch (Exception e) {
            logger.error("Failed to serialize notification metadata: {}", e.getMessage(), e);
            return null; // Graceful degradation - notification will work without metadata
        }
    }

    /**
     * Queues unified notifications for batching (both internal and external users).
     *
     * <p>This method provides a unified approach to batching notifications for both
     * internal users (with user_id) and external users (email-only), eliminating
     * the need for separate batching logic.</p>
     *
     * @param type the notification type (must not be null)
     * @param title the notification title (max 255 characters)
     * @param message the notification message (max 1000 characters)
     * @param recipients the unified recipient list (must not be empty)
     * @param entityReferences optional entity references for deep linking
     * @param metadata optional strongly-typed metadata providing additional context
     * @param urgency notification urgency level (must not be null)
     *
     * @throws IllegalArgumentException if required parameters are invalid
     * @throws NotificationBatchingException if queueing fails
     */
    @Transactional
    public void queueUnifiedNotificationsForBatching(@NotNull @Valid NotificationType type,
                                                     @NotBlank @Size(max = MAX_TITLE_LENGTH) String title,
                                                     @NotBlank @Size(max = MAX_MESSAGE_LENGTH) String message,
                                                     @NotEmpty List<NotificationRecipient> recipients,
                                                     NotificationStorageService.EntityReferences entityReferences,
                                                     @Valid NotificationMetadata metadata,
                                                     @NotNull @Valid NotificationUrgency urgency) {

        logger.debug(QUEUE_DEBUG_MESSAGE, recipients.size(), type, urgency);

        validateUnifiedQueueParameters(title, message, recipients);

        // All notifications go through batching - no bypassing
        LocalDateTime batchWindowStart = LocalDateTime.now();
        queueUnifiedNotificationsInBatch(recipients, type, title, message, entityReferences, metadata, urgency, batchWindowStart);

        logger.info("Queued {} unified notifications for batching at {}", recipients.size(), batchWindowStart);
    }

    /**
     * Queues unified notifications in a batch with the same window start time.
     */
    private void queueUnifiedNotificationsInBatch(List<NotificationRecipient> recipients,
                                                  NotificationType type, String title, String message,
                                                  NotificationStorageService.EntityReferences entityReferences,
                                                  NotificationMetadata metadata, NotificationUrgency urgency,
                                                  LocalDateTime batchWindowStart) {
        for (NotificationRecipient recipient : recipients) {
            queueSingleUnifiedNotification(recipient, type, title, message, entityReferences,
                                          metadata, urgency, batchWindowStart);
        }
    }

    /**
     * Queues a single notification for a unified recipient with comprehensive validation.
     */
    private void queueSingleUnifiedNotification(@NotNull NotificationRecipient recipient,
                                               @NotNull NotificationType type,
                                               @NotBlank String title,
                                               @NotBlank String message,
                                               NotificationStorageService.EntityReferences entityReferences,
                                               NotificationMetadata metadata,
                                               @NotNull NotificationUrgency urgency,
                                               @NotNull LocalDateTime batchWindowStart) {

        try {
            NotificationBatchQueue queueEntry = createUnifiedQueueEntry(recipient, type, title, message, urgency, batchWindowStart);
            setEntityReferences(queueEntry, entityReferences);
            setMetadata(queueEntry, metadata);

            batchQueueRepository.insert(queueEntry);
            logger.debug(QUEUE_SINGLE_DEBUG_MESSAGE, recipient.getUniqueIdentifier(), batchWindowStart);

        } catch (Exception e) {
            logger.error("Failed to queue notification for unified recipient {}: {}", recipient.getUniqueIdentifier(), e.getMessage(), e);
            throw new NotificationBatchingException("Failed to queue notification for unified recipient " + recipient.getUniqueIdentifier(), e);
        }
    }

    /**
     * Creates a basic queue entry for a unified recipient.
     */
    private NotificationBatchQueue createUnifiedQueueEntry(NotificationRecipient recipient,
                                                          NotificationType type, String title, String message,
                                                          NotificationUrgency urgency, LocalDateTime batchWindowStart) {
        NotificationBatchQueue queueEntry = new NotificationBatchQueue();

        queueEntry.setUserId(recipient.userId());
        queueEntry.setEmail(recipient.email());

        queueEntry.setNotificationType(type.toJooqEnum());
        queueEntry.setTitle(title);
        queueEntry.setMessage(message);
        queueEntry.setUrgency(urgency.toJooqEnum());
        queueEntry.setStatus(BatchQueueStatus.PENDING.name());
        queueEntry.setBatchWindowStart(batchWindowStart);
        queueEntry.setCreatedAt(LocalDateTime.now());

        return queueEntry;
    }

    /**
     * Validates unified queue parameters for consistency and business rules.
     */
    private void validateUnifiedQueueParameters(@NotBlank String title,
                                                @NotBlank String message,
                                                @NotEmpty List<NotificationRecipient> recipients) {

        if (title.length() > MAX_TITLE_LENGTH) {
            throw new IllegalArgumentException("Title exceeds maximum length of " + MAX_TITLE_LENGTH + " characters");
        }

        if (message.length() > MAX_MESSAGE_LENGTH) {
            throw new IllegalArgumentException("Message exceeds maximum length of " + MAX_MESSAGE_LENGTH + " characters");
        }

        if (recipients.isEmpty()) {
            throw new IllegalArgumentException("Recipients list cannot be empty");
        }

        // Validate all recipients
        for (NotificationRecipient recipient : recipients) {
            recipient.validate();
        }
    }
}
