package com.collabhub.be.modules.notifications.service;

import com.collabhub.be.modules.auth.dto.GeneralAccessRedirectContext;
import com.collabhub.be.modules.auth.dto.RedirectContext;
import com.collabhub.be.modules.auth.repository.UserRepository;
import com.collabhub.be.modules.auth.service.EmailService;
import com.collabhub.be.modules.auth.service.ExternalUserMagicLinkService;
import com.collabhub.be.modules.collaborationhub.repository.CollaborationHubRepositoryImpl;
import com.collabhub.be.modules.collaborationhub.repository.HubParticipantRepositoryImpl;
import com.collabhub.be.modules.notifications.dto.NotificationChannel;
import com.collabhub.be.modules.notifications.dto.NotificationType;
import com.collabhub.be.modules.notifications.dto.NotificationUrgency;
import org.jooq.generated.tables.pojos.HubParticipant;
import org.jooq.generated.tables.pojos.NotificationBatchQueue;
import org.jooq.generated.tables.pojos.User;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Production-grade service for sending batched email notifications to both internal and external users.
 *
 * <p>This service consolidates multiple notifications into a single email to reduce email spam
 * and improve user experience. It supports both internal users (with user accounts) and external
 * participants (email-only identification) with appropriate URL generation and content customization.</p>
 *
 * <h3>Key Features:</h3>
 * <ul>
 *   <li>Batched email notifications with intelligent grouping by type</li>
 *   <li>User preference validation before sending</li>
 *   <li>Magic link generation for external users</li>
 *   <li>Secure unsubscribe token generation</li>
 *   <li>Comprehensive error handling and logging</li>
 * </ul>
 *
 * <AUTHOR> Hub Team
 * @since 1.0.0
 */
@Service
public class BatchedEmailNotificationService {

    private static final Logger logger = LoggerFactory.getLogger(BatchedEmailNotificationService.class);

    // Email template constants
    private static final String BATCHED_NOTIFICATIONS_TEMPLATE = "email/batched-notifications";
    private static final String DEFAULT_PARTICIPANT_NAME = "Participant";
    private static final String NOTIFICATIONS_PATH = "/app/notifications";
    private static final String LOGIN_PATH = "/login";
    private static final String MAGIC_LINK_PATH = "/auth/magic-link";
    private static final String UNSUBSCRIBE_PATH = "/unsubscribe";
    private static final String UNSUBSCRIBE_API_PATH = "/api/notifications/unsubscribe/process";

    // Date and time formatting
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm");
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("MMM dd, yyyy");

    // Email subject templates
    private static final String SINGLE_NOTIFICATION_SUBJECT_TEMPLATE = "%s";
    private static final String SAME_TYPE_SUBJECT_TEMPLATE = "%d new %s notifications";
    private static final String MIXED_TYPE_SUBJECT_TEMPLATE = "%d new notifications from Collaboration Hub";

    // Dependencies
    private final EmailService emailService;
    private final UserRepository userRepository;
    private final TemplateEngine templateEngine;
    private final NotificationPreferenceService notificationPreferenceService;
    private final ExternalUserMagicLinkService magicLinkService;
    private final UnsubscribeTokenService unsubscribeTokenService;
    private final HubParticipantRepositoryImpl hubParticipantRepository;
    private final CollaborationHubRepositoryImpl hubRepository;

    // Configuration properties
    private final String frontendUrl;
    private final String baseUrl;

    /**
     * Constructs a new BatchedEmailNotificationService with required dependencies.
     *
     * @param emailService service for sending emails
     * @param userRepository repository for user data access
     * @param templateEngine Thymeleaf template engine for email content generation
     * @param notificationPreferenceService service for checking user notification preferences
     * @param magicLinkService service for generating magic links for external users
     * @param unsubscribeTokenService service for generating secure unsubscribe tokens
     * @param hubParticipantRepository repository for hub participant data access
     * @param hubRepository repository for collaboration hub data access
     */
    public BatchedEmailNotificationService(EmailService emailService,
                                         UserRepository userRepository,
                                         TemplateEngine templateEngine,
                                         NotificationPreferenceService notificationPreferenceService,
                                         ExternalUserMagicLinkService magicLinkService,
                                         UnsubscribeTokenService unsubscribeTokenService,
                                         HubParticipantRepositoryImpl hubParticipantRepository,
                                         CollaborationHubRepositoryImpl hubRepository,
                                         @Value("${app.frontend-url}") String frontendUrl,
                                         @Value("${app.base-url}") String baseUrl) {
        this.emailService = emailService;
        this.userRepository = userRepository;
        this.templateEngine = templateEngine;
        this.notificationPreferenceService = notificationPreferenceService;
        this.magicLinkService = magicLinkService;
        this.unsubscribeTokenService = unsubscribeTokenService;
        this.hubParticipantRepository = hubParticipantRepository;
        this.hubRepository = hubRepository;
        this.frontendUrl = frontendUrl;
        this.baseUrl = baseUrl;
    }

    /**
     * Sends a batched email containing multiple notifications to an internal user.
     *
     * <p>This method validates the user exists, checks their notification preferences,
     * groups notifications by type, and sends a consolidated email using the appropriate template.</p>
     *
     * @param userId the internal user ID to send the email to (must be positive)
     * @param notifications list of notifications to include in the batch (must not be null or empty)
     * @return true if email was sent successfully or user preferences prevented sending, false on error
     * @throws IllegalArgumentException if userId is null or negative, or notifications is null
     */
    public boolean sendBatchedEmail(Long userId, List<NotificationBatchQueue> notifications) {
        return sendBatchedEmailToInternalUser(userId, notifications);
    }

    /**
     * Sends a batched email containing multiple notifications to an external participant.
     *
     * <p>This method validates the email address, checks notification preferences for external users,
     * groups notifications by type, and sends a consolidated email with magic links for access.</p>
     *
     * @param email the email address to send the email to (must not be null or blank)
     * @param notifications list of notifications to include in the batch (must not be null or empty)
     * @return true if email was sent successfully or user preferences prevented sending, false on error
     * @throws IllegalArgumentException if email is null or blank, or notifications is null
     */
    public boolean sendBatchedEmailToExternal(String email, List<NotificationBatchQueue> notifications) {
        return sendBatchedEmailToExternalUser(email, notifications);
    }

    /**
     * Sends a batched email containing multiple notifications to an internal user.
     *
     * <p>This is the main implementation method that validates the user exists, checks their
     * notification preferences, groups notifications by type, and sends a consolidated email
     * using the appropriate template.</p>
     *
     * @param userId the internal user ID to send the email to (must be positive)
     * @param notifications list of notifications to include in the batch (must not be null or empty)
     * @return true if email was sent successfully or user preferences prevented sending, false on error
     * @throws IllegalArgumentException if userId is null or negative, or notifications is null
     */
    private boolean sendBatchedEmailToInternalUser(Long userId, List<NotificationBatchQueue> notifications) {
        validateInternalUserParameters(userId, notifications);

        if (notifications.isEmpty()) {
            logger.debug("No notifications to send for internal user {}", userId);
            return true;
        }

        logger.debug("Processing batched email for internal user {} with {} notifications",
                    userId, notifications.size());

        try {
            var user = findUserById(userId);
            if (user == null) {
                return false;
            }

            if (!shouldSendNotificationsToUser(user.getEmail(), notifications)) {
                logger.debug("Internal user {} has disabled email notifications for these types", userId);
                return true; // Respect user preferences
            }

            return sendEmailToInternalUser(user, notifications);

        } catch (Exception e) {
            logger.error("Failed to send batched email to internal user {}: {}", userId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * Sends a batched email containing multiple notifications to an external participant.
     *
     * <p>This is the main implementation method that validates the email address, checks
     * notification preferences for external users, groups notifications by type, and sends
     * a consolidated email with magic links for access.</p>
     *
     * @param email the email address to send the email to (must not be null or blank)
     * @param notifications list of notifications to include in the batch (must not be null or empty)
     * @return true if email was sent successfully or user preferences prevented sending, false on error
     * @throws IllegalArgumentException if email is null or blank, or notifications is null
     */
    private boolean sendBatchedEmailToExternalUser(String email, List<NotificationBatchQueue> notifications) {
        validateExternalUserParameters(email, notifications);

        if (notifications.isEmpty()) {
            logger.debug("No notifications to send for external participant {}", email);
            return true;
        }

        logger.debug("Processing batched email for external participant {} with {} notifications",
                    email, notifications.size());

        try {
            var normalizedEmail = normalizeEmail(email);

            if (!shouldSendNotificationsToExternalUser(normalizedEmail, notifications)) {
                logger.debug("External participant {} has disabled email notifications for these types", normalizedEmail);
                return true; // Respect user preferences
            }

            return sendEmailToExternalUser(normalizedEmail, notifications);

        } catch (Exception e) {
            logger.error("Failed to send batched email to external participant {}: {}", email, e.getMessage(), e);
            return false;
        }
    }

    /**
     * Validates parameters for internal user email sending.
     */
    private void validateInternalUserParameters(Long userId, List<NotificationBatchQueue> notifications) {
        if (userId == null || userId <= 0) {
            throw new IllegalArgumentException("User ID must be positive");
        }
        if (notifications == null) {
            throw new IllegalArgumentException("Notifications list cannot be null");
        }
    }

    /**
     * Validates parameters for external user email sending.
     */
    private void validateExternalUserParameters(String email, List<NotificationBatchQueue> notifications) {
        if (email == null || email.trim().isEmpty()) {
            throw new IllegalArgumentException("Email address cannot be null or blank");
        }
        if (notifications == null) {
            throw new IllegalArgumentException("Notifications list cannot be null");
        }
    }

    /**
     * Normalizes an email address to lowercase and trims whitespace.
     */
    private String normalizeEmail(String email) {
        return email.toLowerCase().trim();
    }

    /**
     * Finds a user by ID and validates the user has a valid email address.
     */
    private User findUserById(Long userId) {
        var user = userRepository.findById(userId);
        if (user == null) {
            logger.warn("Internal user {} not found", userId);
            return null;
        }

        if (user.getEmail() == null || user.getEmail().trim().isEmpty()) {
            logger.warn("Internal user {} has no email address", userId);
            return null;
        }

        return user;
    }

    /**
     * Checks if notifications should be sent to an internal user based on their preferences.
     */
    private boolean shouldSendNotificationsToUser(String email, List<NotificationBatchQueue> notifications) {
        return notifications.stream()
                .map(notification -> NotificationType.valueOf(notification.getNotificationType().name()))
                .anyMatch(type -> notificationPreferenceService.shouldSendEmailNotification(email, type));
    }

    /**
     * Checks if notifications should be sent to an external user based on their preferences.
     */
    private boolean shouldSendNotificationsToExternalUser(String email, List<NotificationBatchQueue> notifications) {
        return notifications.stream()
                .map(notification -> NotificationType.valueOf(notification.getNotificationType().name()))
                .anyMatch(type -> {
                    Boolean isEnabled = notificationPreferenceService.isExternalNotificationEnabled(
                            email, type, NotificationChannel.EMAIL);
                    return isEnabled == null || isEnabled; // Default to enabled if no preference set
                });
    }

    /**
     * Sends the actual email to an internal user.
     */
    private boolean sendEmailToInternalUser(User user, List<NotificationBatchQueue> notifications) {
        var notificationsByType = groupNotificationsByType(notifications);
        var subject = generateEmailSubject(notifications.size(), notificationsByType);
        var htmlContent = generateInternalUserEmailContent(user, notifications, notificationsByType);

        var sent = emailService.sendHtmlEmail(user.getEmail(), subject, htmlContent);
        logEmailResult(sent, "internal user", user.getEmail(), notifications.size());
        return sent;
    }

    /**
     * Sends the actual email to an external user.
     */
    private boolean sendEmailToExternalUser(String email, List<NotificationBatchQueue> notifications) {
        var notificationsByType = groupNotificationsByType(notifications);
        var subject = generateEmailSubject(notifications.size(), notificationsByType);
        var htmlContent = generateExternalUserEmailContent(email, notifications, notificationsByType);

        var sent = emailService.sendHtmlEmail(email, subject, htmlContent);
        logEmailResult(sent, "external participant", email, notifications.size());
        return sent;
    }

    /**
     * Groups notifications by their type for better organization in emails.
     */
    private Map<NotificationType, List<NotificationBatchQueue>> groupNotificationsByType(List<NotificationBatchQueue> notifications) {
        return notifications.stream()
                .collect(Collectors.groupingBy(n -> NotificationType.valueOf(n.getNotificationType().name())));
    }

    /**
     * Logs the result of email sending operation.
     */
    private void logEmailResult(boolean sent, String userType, String email, int notificationCount) {
        if (sent) {
            logger.info("Successfully sent batched email to {} {} with {} notifications",
                       userType, email, notificationCount);
        } else {
            logger.warn("Failed to send batched email to {} {}", userType, email);
        }
    }

    /**
     * Generates an appropriate email subject line based on notification count and types.
     *
     * <p>Uses different templates based on whether there's a single notification,
     * multiple notifications of the same type, or mixed notification types.</p>
     */
    private String generateEmailSubject(int notificationCount,
                                      Map<NotificationType, List<NotificationBatchQueue>> notificationsByType) {
        if (notificationCount == 1) {
            return generateSingleNotificationSubject(notificationsByType);
        }

        if (notificationsByType.size() == 1) {
            return generateSameTypeNotificationsSubject(notificationCount, notificationsByType);
        }

        return String.format(MIXED_TYPE_SUBJECT_TEMPLATE, notificationCount);
    }

    /**
     * Generates subject for a single notification email.
     */
    private String generateSingleNotificationSubject(Map<NotificationType, List<NotificationBatchQueue>> notificationsByType) {
        var firstType = notificationsByType.keySet().iterator().next();
        var firstNotification = notificationsByType.get(firstType).get(0);
        return String.format(SINGLE_NOTIFICATION_SUBJECT_TEMPLATE, firstNotification.getTitle());
    }

    /**
     * Generates subject for multiple notifications of the same type.
     */
    private String generateSameTypeNotificationsSubject(int count, Map<NotificationType, List<NotificationBatchQueue>> notificationsByType) {
        var type = notificationsByType.keySet().iterator().next();
        var displayName = getNotificationTypeDisplayName(type).toLowerCase();
        return String.format(SAME_TYPE_SUBJECT_TEMPLATE, count, displayName);
    }

    /**
     * Generates HTML email content for internal users using Thymeleaf template.
     *
     * <p>Creates a template context with user information, notification groups,
     * timing data, and appropriate URLs for internal user access.</p>
     */
    private String generateInternalUserEmailContent(User user, List<NotificationBatchQueue> notifications,
                                                   Map<NotificationType, List<NotificationBatchQueue>> notificationsByType) {
        var context = createBaseEmailContext(notifications, notificationsByType);

        // Internal user specific information
        var displayName = user.getDisplayName() != null ? user.getDisplayName() : user.getEmail();
        context.setVariable("userName", displayName);
        context.setVariable("userEmail", user.getEmail());
        context.setVariable("isExternalParticipant", false);

        // Generate URLs for internal users
        var viewAllNotificationsUrl = generateInternalUserNotificationsUrl();
        var unsubscribeUrl = generateUnsubscribeUrl(user.getEmail());
        setUrlVariables(context, viewAllNotificationsUrl, unsubscribeUrl);

        return templateEngine.process(BATCHED_NOTIFICATIONS_TEMPLATE, context);
    }

    /**
     * Generates HTML email content for external users using Thymeleaf template.
     *
     * <p>Creates a template context with participant information, notification groups,
     * timing data, and magic links for external user access.</p>
     */
    private String generateExternalUserEmailContent(String email, List<NotificationBatchQueue> notifications,
                                                   Map<NotificationType, List<NotificationBatchQueue>> notificationsByType) {
        var context = createBaseEmailContext(notifications, notificationsByType);

        // External participant specific information
        var displayName = extractDisplayNameFromEmail(email);
        context.setVariable("userName", displayName);
        context.setVariable("userEmail", email);
        context.setVariable("isExternalParticipant", true);

        // Generate URLs for external users
        var viewAllNotificationsUrl = generateExternalUserNotificationsUrl(email);
        var unsubscribeUrl = generateUnsubscribeUrl(email);
        setUrlVariables(context, viewAllNotificationsUrl, unsubscribeUrl);

        return templateEngine.process(BATCHED_NOTIFICATIONS_TEMPLATE, context);
    }

    /**
     * Creates the base email template context with common notification data.
     */
    private Context createBaseEmailContext(List<NotificationBatchQueue> notifications,
                                         Map<NotificationType, List<NotificationBatchQueue>> notificationsByType) {
        var context = new Context();

        // Notification summary
        context.setVariable("totalNotifications", notifications.size());
        context.setVariable("notificationTypes", notificationsByType.size());

        // Group notifications for display
        var notificationGroups = createNotificationGroups(notificationsByType);
        context.setVariable("notificationGroups", notificationGroups);

        // Timing information
        var firstNotification = notifications.get(0);
        context.setVariable("batchTime", firstNotification.getCreatedAt().format(TIME_FORMATTER));
        context.setVariable("batchDate", firstNotification.getCreatedAt().format(DATE_FORMATTER));

        return context;
    }

    /**
     * Creates notification groups for email template display.
     */
    private List<NotificationGroup> createNotificationGroups(Map<NotificationType, List<NotificationBatchQueue>> notificationsByType) {
        return notificationsByType.entrySet().stream()
                .map(entry -> new NotificationGroup(
                    entry.getKey(),
                    getNotificationTypeDisplayName(entry.getKey()),
                    entry.getValue().stream()
                        .map(this::convertToDisplayNotification)
                        .collect(Collectors.toList())
                ))
                .collect(Collectors.toList());
    }

    /**
     * Sets URL variables in the email template context.
     */
    private void setUrlVariables(Context context, String viewAllNotificationsUrl, String unsubscribeUrl) {
        context.setVariable("viewAllNotificationsUrl", viewAllNotificationsUrl);
        context.setVariable("unsubscribeUrl", unsubscribeUrl);
        context.setVariable("frontendUrl", frontendUrl);
    }

    /**
     * Extracts a user-friendly display name from an email address for external participants.
     *
     * <p>Takes the local part of the email (before @) and capitalizes the first letter.</p>
     */
    private String extractDisplayNameFromEmail(String email) {
        if (email == null || email.trim().isEmpty()) {
            return DEFAULT_PARTICIPANT_NAME;
        }

        var emailParts = email.split("@");
        if (emailParts.length == 0 || emailParts[0].isEmpty()) {
            return DEFAULT_PARTICIPANT_NAME;
        }

        var localPart = emailParts[0];
        return localPart.substring(0, 1).toUpperCase() + localPart.substring(1).toLowerCase();
    }

    /**
     * Converts a notification batch queue item to a display-friendly format for email templates.
     */
    private DisplayNotification convertToDisplayNotification(NotificationBatchQueue notification) {
        return new DisplayNotification(
            notification.getTitle(),
            notification.getMessage(),
            notification.getCreatedAt().format(TIME_FORMATTER),
            NotificationUrgency.fromJooqEnum(notification.getUrgency()),
            generateNotificationDeepLink(notification)
        );
    }

    /**
     * Generates a deep link URL for a specific notification based on its type and entity references.
     *
     * <p>This method creates specific URLs that navigate users directly to the relevant content
     * within the collaboration hub interface. The URLs include query parameters for tab navigation
     * and entity-specific anchors for precise targeting.</p>
     *
     * <h3>URL Patterns Generated:</h3>
     * <ul>
     *   <li>Hub invitations: {@code /app/collaboration-hubs/{hubId}}</li>
     *   <li>Post-related: {@code /app/collaboration-hubs/{hubId}?tab=posts&post={postId}}</li>
     *   <li>Comment-related: {@code /app/collaboration-hubs/{hubId}?tab=posts&post={postId}#comment-{commentId}}</li>
     *   <li>Chat-related: {@code /app/collaboration-hubs/{hubId}?tab=chat&chat={chatChannelId}}</li>
     *   <li>Brief-related: {@code /app/collaboration-hubs/{hubId}?tab=briefs&brief={briefId}}</li>
     *   <li>Fallback: {@code /app/dashboard} (when no hub ID available)</li>
     * </ul>
     *
     * @param notification the notification containing entity references and type information
     * @return a deep link URL string that navigates to the specific content
     */
    private String generateNotificationDeepLink(NotificationBatchQueue notification) {
        var hubId = notification.getCollaborationHubId();

        // If no hub ID is available, navigate to dashboard as fallback
        if (hubId == null) {
            logger.debug("No hub ID found for notification {}, using dashboard fallback",
                        notification.getId());
            return "/app/dashboard";
        }

        var baseUrl = "/app/collaboration-hubs/" + hubId;
        var notificationType = NotificationType.valueOf(notification.getNotificationType().name());

        return switch (notificationType) {
            case CHAT_MENTION, CHAT_ADDED -> generateChatDeepLink(baseUrl, notification);
            case COMMENT_ADDED, COMMENT_MENTION, POST_REVIEWED, POST_EDITED, ASSIGNED_AS_REVIEWER ->
                generatePostDeepLink(baseUrl, notification);
            case BRIEF_CREATED, BRIEF_UPDATED, BRIEF_ASSIGNED ->
                generateBriefDeepLink(baseUrl, notification);
            case INVITE_TO_HUB -> baseUrl; // Direct hub access for invitations
        };
    }

    /**
     * Generates a deep link URL for chat-related notifications.
     *
     * <p>Creates URLs that navigate to the chat tab with optional channel selection.
     * If a specific chat channel ID is available, it will be included as a query parameter.</p>
     *
     * @param baseUrl the base collaboration hub URL
     * @param notification the notification containing chat entity references
     * @return a chat-specific deep link URL
     */
    private String generateChatDeepLink(String baseUrl, NotificationBatchQueue notification) {
        var chatChannelId = notification.getChatChannelId();

        if (chatChannelId != null) {
            return baseUrl + "?tab=chat&chat=" + chatChannelId;
        }

        return baseUrl + "?tab=chat";
    }

    /**
     * Generates a deep link URL for post-related notifications.
     *
     * <p>Creates URLs that navigate to the posts tab with optional post selection and comment anchors.
     * For comment-related notifications, includes a fragment identifier to scroll to the specific comment.</p>
     *
     * @param baseUrl the base collaboration hub URL
     * @param notification the notification containing post and comment entity references
     * @return a post-specific deep link URL
     */
    private String generatePostDeepLink(String baseUrl, NotificationBatchQueue notification) {
        var postId = notification.getPostId();
        var commentId = notification.getCommentId();

        if (postId != null) {
            var url = baseUrl + "?tab=posts&post=" + postId;

            // Add comment anchor for comment-specific notifications
            if (commentId != null) {
                url += "#comment-" + commentId;
            }

            return url;
        }

        return baseUrl + "?tab=posts";
    }

    /**
     * Generates a deep link URL for brief-related notifications.
     *
     * <p>Creates URLs that navigate to the briefs tab with optional brief selection.</p>
     *
     * @param baseUrl the base collaboration hub URL
     * @param notification the notification containing brief entity references
     * @return a brief-specific deep link URL
     */
    private String generateBriefDeepLink(String baseUrl, NotificationBatchQueue notification) {
        var briefId = notification.getBriefId();

        if (briefId != null) {
            return baseUrl + "?tab=briefs&brief=" + briefId;
        }

        return baseUrl + "?tab=briefs";
    }

    /**
     * Generates the "View All Notifications" URL for internal users.
     *
     * <p>Internal users are redirected to the login page with a redirect parameter
     * that will take them to the notifications page after authentication.</p>
     */
    private String generateInternalUserNotificationsUrl() {
        var encodedRedirect = URLEncoder.encode(NOTIFICATIONS_PATH, StandardCharsets.UTF_8);
        return frontendUrl + LOGIN_PATH + "?redirect=" + encodedRedirect;
    }

    /**
     * Generates the "View All Notifications" URL for external users.
     *
     * <p>External users get a magic link that automatically authenticates them
     * and redirects to the notifications page. Falls back to login page if
     * magic link generation fails.</p>
     */
    private String generateExternalUserNotificationsUrl(String email) {
        try {
            var accountId = findAccountIdForExternalUser(email);
            if (accountId == null) {
                logger.debug("No hub access found for external user {}, using login fallback", email);
                return frontendUrl + LOGIN_PATH;
            }

            var magicLinkToken = createMagicLinkToken(email, accountId);
            return frontendUrl + MAGIC_LINK_PATH + "?token=" + magicLinkToken;

        } catch (Exception e) {
            logger.warn("Failed to generate magic link for external user {}: {}", email, e.getMessage());
            return frontendUrl + LOGIN_PATH;
        }
    }

    /**
     * Finds the account ID for an external user by looking up their hub participation.
     */
    private Long findAccountIdForExternalUser(String email) {
        var participants = hubParticipantRepository.findActiveParticipantsByEmailAndAccount(email);
        if (participants.isEmpty()) {
            return null;
        }

        var hubId = participants.getFirst().getHubId();
        return hubRepository.getAccountIdForHub(hubId);
    }

    /**
     * Creates a magic link token for external user authentication.
     */
    private String createMagicLinkToken(String email, Long accountId) {
        var redirectContext = new GeneralAccessRedirectContext(NOTIFICATIONS_PATH);
        return magicLinkService.createMagicLinkForEmailWithRedirect(email, accountId, redirectContext);
    }

    /**
     * Generates a secure unsubscribe URL for any user (internal or external).
     *
     * <p>Creates a one-time use token that allows users to unsubscribe from
     * notification emails without requiring authentication. Falls back to
     * a generic unsubscribe page if token generation fails.</p>
     */
    private String generateUnsubscribeUrl(String email) {
        try {
            var unsubscribeToken = unsubscribeTokenService.createUnsubscribeToken(email);
            return baseUrl + UNSUBSCRIBE_API_PATH + "?token=" + unsubscribeToken;
        } catch (Exception e) {
            logger.warn("Failed to generate unsubscribe token for email {}: {}", email, e.getMessage());
            return frontendUrl + UNSUBSCRIBE_PATH;
        }
    }

    /**
     * Gets a user-friendly display name for a notification type.
     *
     * <p>Converts technical notification type enums into readable strings
     * suitable for display in email templates and user interfaces.</p>
     */
    private String getNotificationTypeDisplayName(NotificationType type) {
        return switch (type) {
            case INVITE_TO_HUB -> "Hub Invitations";
            case ASSIGNED_AS_REVIEWER -> "Review Assignments";
            case POST_REVIEWED -> "Post Reviews";
            case POST_EDITED -> "Post Edits";
            case COMMENT_ADDED -> "Comments";
            case COMMENT_MENTION -> "Comment Mentions";
            case CHAT_MENTION -> "Chat Mentions";
            case CHAT_ADDED -> "Chat Additions";
            case BRIEF_CREATED -> "New Briefs";
            case BRIEF_UPDATED -> "Brief Updates";
            case BRIEF_ASSIGNED -> "Brief Assignments";
        };
    }

    /**
     * Immutable data class representing a group of notifications of the same type for email display.
     *
     * <p>This class groups notifications by their type to provide better organization
     * in email templates. Each group contains the notification type, a user-friendly
     * display name, and the list of notifications in that group.</p>
     *
     * @param type the notification type for this group
     * @param displayName user-friendly name for display in email templates
     * @param notifications list of notifications in this group
     */
    public record NotificationGroup(
            NotificationType type,
            String displayName,
            List<DisplayNotification> notifications
    ) {
        /**
         * Gets the count of notifications in this group.
         *
         * @return the number of notifications in this group
         */
        public int getCount() {
            return notifications.size();
        }
    }

    /**
     * Immutable data class representing a single notification formatted for email display.
     *
     * <p>This class contains all the information needed to display a notification
     * in an email template, including formatted time, urgency level, and deep link URL.</p>
     *
     * @param title the notification title
     * @param message the notification message content
     * @param time formatted time string for display
     * @param urgency the urgency level of the notification
     * @param url deep link URL for the notification
     */
    public record DisplayNotification(
            String title,
            String message,
            String time,
            NotificationUrgency urgency,
            String url
    ) {
        /**
         * Determines if this notification is high priority based on urgency level.
         *
         * @return true if the notification has HIGH or URGENT urgency, false otherwise
         */
        public boolean isHighPriority() {
            return urgency == NotificationUrgency.HIGH || urgency == NotificationUrgency.URGENT;
        }
    }
}
