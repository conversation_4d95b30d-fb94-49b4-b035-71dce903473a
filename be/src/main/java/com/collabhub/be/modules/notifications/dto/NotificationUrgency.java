package com.collabhub.be.modules.notifications.dto;

import org.jooq.generated.enums.NotificationUrgencyEnum;

/**
 * Enum representing the urgency levels of notifications.
 * Determines batching behavior and delivery timing.
 * Maps to the database notification_urgency_enum for consistency.
 */
public enum NotificationUrgency {
    /**
     * Low priority notifications.
     * Examples: General comments, non-critical updates.
     */
    LOW(NotificationUrgencyEnum.LOW),

    /**
     * Normal priority notifications.
     * Examples: Post reviews, brief updates, general assignments.
     */
    NORMAL(NotificationUrgencyEnum.NORMAL),

    /**
     * High priority notifications.
     * Examples: Direct mentions, reviewer assignments, hub invitations.
     */
    HIGH(NotificationUrgencyEnum.HIGH),

    /**
     * Urgent notifications.
     * Examples: Security alerts, account issues, critical system notifications.
     */
    URGENT(NotificationUrgencyEnum.URGENT);

    private final NotificationUrgencyEnum jooqEnum;

    NotificationUrgency(NotificationUrgencyEnum jooqEnum) {
        this.jooqEnum = jooqEnum;
    }

    /**
     * Returns the corresponding jOOQ enum for database operations.
     */
    public NotificationUrgencyEnum toJooqEnum() {
        return jooqEnum;
    }

    /**
     * Converts from jOOQ enum to application enum.
     */
    public static NotificationUrgency fromJooqEnum(NotificationUrgencyEnum jooqEnum) {
        if (jooqEnum == null) {
            return NORMAL; // Default fallback
        }
        
        for (NotificationUrgency urgency : values()) {
            if (urgency.jooqEnum == jooqEnum) {
                return urgency;
            }
        }
        
        return NORMAL; // Fallback for unknown values
    }



    /**
     * Determines the default urgency for a notification type.
     * This provides sensible defaults that can be overridden when creating notifications.
     */
    public static NotificationUrgency getDefaultForType(NotificationType type) {
        return switch (type) {
            case INVITE_TO_HUB -> HIGH;           // Important invitations
            case ASSIGNED_AS_REVIEWER -> HIGH;    // Direct assignments
            case POST_REVIEWED -> NORMAL;         // Standard workflow
            case POST_EDITED -> NORMAL;           // Post edits after review
            case COMMENT_ADDED -> LOW;            // General activity
            case COMMENT_MENTION -> HIGH;         // Direct mentions
            case CHAT_MENTION -> HIGH;            // Direct mentions
            case CHAT_ADDED -> NORMAL;            // Channel additions
            case BRIEF_CREATED -> NORMAL;         // New briefs
            case BRIEF_UPDATED -> LOW;            // Brief changes
            case BRIEF_ASSIGNED -> HIGH;          // Direct assignments
        };
    }
}
