package com.collabhub.be.modules.notifications.dto;

import org.jooq.generated.enums.NotificationTypeEnum;

/**
 * Enum representing the types of notifications that can be sent to users.
 * Maps to the database notification_type_enum for consistency.
 */
public enum NotificationType {
    /**
     * User is invited to join a collaboration hub.
     */
    INVITE_TO_HUB(NotificationTypeEnum.INVITE_TO_HUB),

    /**
     * User is assigned as a reviewer for a post.
     */
    ASSIGNED_AS_REVIEWER(NotificationTypeEnum.ASSIGNED_AS_REVIEWER),

    /**
     * A post that the user created or is involved with has been reviewed.
     */
    POST_REVIEWED(NotificationTypeEnum.POST_REVIEWED),

    /**
     * A post that has been reviewed has been edited by its creator.
     */
    POST_EDITED(NotificationTypeEnum.POST_EDITED),

    /**
     * A comment has been added to a post the user is involved with.
     */
    COMMENT_ADDED(NotificationTypeEnum.COMMENT_ADDED),

    /**
     * User is mentioned in a post comment.
     */
    COMMENT_MENTION(NotificationTypeEnum.COMMENT_MENTION),

    /**
     * User is mentioned in a chat message.
     */
    CHAT_MENTION(NotificationTypeEnum.CHAT_MENTION),

    /**
     * User is added to a chat channel.
     */
    CHAT_ADDED(NotificationTypeEnum.CHAT_ADDED),

    /**
     * A new brief has been created in a collaboration hub.
     */
    BRIEF_CREATED(NotificationTypeEnum.BRIEF_CREATED),

    /**
     * A brief has been updated in a collaboration hub.
     */
    BRIEF_UPDATED(NotificationTypeEnum.BRIEF_UPDATED),

    /**
     * User is assigned to work on a brief.
     */
    BRIEF_ASSIGNED(NotificationTypeEnum.BRIEF_ASSIGNED);

    private final NotificationTypeEnum jooqEnum;

    NotificationType(NotificationTypeEnum jooqEnum) {
        this.jooqEnum = jooqEnum;
    }

    /**
     * Returns the corresponding jOOQ enum for database operations.
     */
    public NotificationTypeEnum toJooqEnum() {
        return jooqEnum;
    }

    /**
     * Converts from jOOQ enum to DTO enum.
     */
    public static NotificationType fromJooqEnum(NotificationTypeEnum jooqEnum) {
        if (jooqEnum == null) {
            return null;
        }

        for (NotificationType type : values()) {
            if (type.jooqEnum == jooqEnum) {
                return type;
            }
        }

        throw new IllegalArgumentException("Unknown NotificationTypeEnum: " + jooqEnum);
    }
}
