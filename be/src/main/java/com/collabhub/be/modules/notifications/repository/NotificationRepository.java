package com.collabhub.be.modules.notifications.repository;

import com.collabhub.be.modules.notifications.dto.NotificationStatus;
import com.collabhub.be.modules.notifications.exception.NotificationStorageException;
import org.jooq.DSLContext;
import org.jooq.generated.tables.daos.NotificationDao;
import org.jooq.generated.tables.pojos.Notification;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

import static org.jooq.generated.Tables.NOTIFICATION;

/**
 * Production-grade repository for Notification entity using jOOQ for database operations.
 *
 * <p>This repository provides multi-tenant aware queries scoped by user_id with comprehensive
 * error handling, bulk operations, and performance optimizations. It uses jOOQ fetchInto()
 * projections for type-safe database access and includes proper indexing strategies.</p>
 *
 * <h3>Key Features:</h3>
 * <ul>
 *   <li>User-scoped queries with security validation</li>
 *   <li>Bulk operations with transaction rollback on failure</li>
 *   <li>0-based pagination with proper offset calculations</li>
 *   <li>Comprehensive error handling and logging</li>
 *   <li>Performance-optimized queries with proper indexing</li>
 * </ul>
 *
 * <AUTHOR> Hub Team
 * @since 1.0.0
 */
@Repository
public class NotificationRepository extends NotificationDao {

    private static final Logger logger = LoggerFactory.getLogger(NotificationRepository.class);

    private final DSLContext dsl;

    public NotificationRepository(DSLContext dsl) {
        super(dsl.configuration());
        this.dsl = dsl;
    }

    // ========================================
    // EXTERNAL USER (EMAIL-BASED) METHODS
    // ========================================

    /**
     * Finds notifications for an external user by email with pagination.
     *
     * <p>This method provides paginated access to notifications for external users
     * identified by email address. It uses optimized queries with proper indexing.</p>
     *
     * @param email the email address (must be valid)
     * @param page the page number (0-based, must be non-negative)
     * @param size the page size (must be positive, max 100)
     * @param unreadOnly whether to return only unread notifications
     * @return list of notifications ordered by creation date (newest first)
     *
     * @throws IllegalArgumentException if parameters are invalid
     * @throws NotificationStorageException if database operation fails
     */
    public List<Notification> findByEmailWithPagination(String email, int page, int size, boolean unreadOnly) {
        validateEmailPaginationParameters(email, page, size);

        try {
            var query = dsl.select()
                    .from(NOTIFICATION)
                    .where(NOTIFICATION.EMAIL.eq(email.toLowerCase().trim()));

            if (unreadOnly) {
                query = query.and(NOTIFICATION.STATUS.eq(NotificationStatus.UNREAD.toJooqEnum()));
            }

            List<Notification> notifications = query
                    .orderBy(NOTIFICATION.CREATED_AT.desc())
                    .limit(size)
                    .offset(page * size)
                    .fetchInto(Notification.class);

            logger.debug("Retrieved {} notifications for external user {} (page={}, size={}, unreadOnly={})",
                        notifications.size(), email, page, size, unreadOnly);

            return notifications;

        } catch (DataAccessException e) {
            logger.error("Failed to retrieve notifications for external user {}: {}", email, e.getMessage(), e);
            throw new NotificationStorageException("Failed to retrieve notifications from database", e);
        }
    }

    /**
     * Counts total notifications for an external user by email.
     *
     * @param email the email address (must be valid)
     * @param unreadOnly whether to count only unread notifications
     * @return total count (non-negative)
     *
     * @throws IllegalArgumentException if email is invalid
     * @throws NotificationStorageException if database operation fails
     */
    public int countByEmail(String email, boolean unreadOnly) {
        validateEmailParameter(email);

        try {
            var query = dsl.selectCount()
                    .from(NOTIFICATION)
                    .where(NOTIFICATION.EMAIL.eq(email.toLowerCase().trim()));

            if (unreadOnly) {
                query = query.and(NOTIFICATION.STATUS.eq(NotificationStatus.UNREAD.toJooqEnum()));
            }

            Integer count = query.fetchOneInto(Integer.class);
            int result = count != null ? count : 0;

            logger.debug("Counted {} notifications for external user {} (unreadOnly={})", result, email, unreadOnly);
            return result;

        } catch (DataAccessException e) {
            logger.error("Failed to count notifications for external user {}: {}", email, e.getMessage(), e);
            throw new NotificationStorageException("Failed to count notifications in database", e);
        }
    }

    /**
     * Marks a specific notification as read for an external user by email.
     *
     * @param notificationId the notification ID (must be positive)
     * @param email the email address for security validation (must be valid)
     * @return true if updated, false if not found or not owned by user
     *
     * @throws IllegalArgumentException if parameters are invalid
     * @throws NotificationStorageException if database operation fails
     */
    public boolean markAsReadByEmail(Long notificationId, String email) {
        validateMarkAsReadByEmailParameters(notificationId, email);

        try {
            LocalDateTime now = LocalDateTime.now();

            int updatedRows = dsl.update(NOTIFICATION)
                    .set(NOTIFICATION.STATUS, NotificationStatus.READ.toJooqEnum())
                    .set(NOTIFICATION.READ_AT, now)
                    .where(NOTIFICATION.ID.eq(notificationId))
                    .and(NOTIFICATION.EMAIL.eq(email.toLowerCase().trim()))
                    .and(NOTIFICATION.STATUS.eq(NotificationStatus.UNREAD.toJooqEnum()))
                    .execute();

            boolean updated = updatedRows > 0;
            logger.debug("Mark as read result for notification {} by external user {}: {}",
                        notificationId, email, updated ? "success" : "not found/already read");

            return updated;

        } catch (DataAccessException e) {
            logger.error("Failed to mark notification {} as read for external user {}: {}",
                        notificationId, email, e.getMessage(), e);
            throw new NotificationStorageException("Failed to mark notification as read", e);
        }
    }

    /**
     * Marks all notifications as read for an external user by email.
     *
     * @param email the email address (must be valid)
     * @return number of notifications marked as read
     *
     * @throws IllegalArgumentException if email is invalid
     * @throws NotificationStorageException if database operation fails
     */
    public int markAllAsReadByEmail(String email) {
        validateEmailParameter(email);

        try {
            LocalDateTime now = LocalDateTime.now();

            int updatedRows = dsl.update(NOTIFICATION)
                    .set(NOTIFICATION.STATUS, NotificationStatus.READ.toJooqEnum())
                    .set(NOTIFICATION.READ_AT, now)
                    .where(NOTIFICATION.EMAIL.eq(email.toLowerCase().trim()))
                    .and(NOTIFICATION.STATUS.eq(NotificationStatus.UNREAD.toJooqEnum()))
                    .execute();

            logger.info("Marked {} notifications as read for external user {}", updatedRows, email);
            return updatedRows;

        } catch (DataAccessException e) {
            logger.error("Failed to mark all notifications as read for external user {}: {}", email, e.getMessage(), e);
            throw new NotificationStorageException("Failed to mark notifications as read", e);
        }
    }

    // ========================================
    // MIXED RECIPIENT BULK OPERATIONS
    // ========================================

    /**
     * Bulk creates notifications for mixed recipients (internal and external users).
     *
     * @param notifications list of notifications with either user_id or email set
     * @return number of notifications successfully created
     *
     * @throws IllegalArgumentException if notifications list is invalid
     * @throws NotificationStorageException if bulk creation fails
     */
    public int bulkCreateMixedNotifications(List<Notification> notifications) {
        if (notifications == null || notifications.isEmpty()) {
            throw new IllegalArgumentException("Notifications list cannot be null or empty");
        }

        // Validate mixed recipient constraints
        validateMixedNotifications(notifications);

        try {
            logger.debug("Bulk creating {} mixed notifications", notifications.size());

            // Convert POJOs to Records for jOOQ batch insert
            List<org.jooq.generated.tables.records.NotificationRecord> records = notifications.stream()
                    .map(notification -> {
                        org.jooq.generated.tables.records.NotificationRecord record = dsl.newRecord(NOTIFICATION);
                        record.setUserId(notification.getUserId());
                        record.setEmail(notification.getEmail());
                        record.setType(notification.getType());
                        record.setTitle(notification.getTitle());
                        record.setMessage(notification.getMessage());
                        record.setStatus(notification.getStatus());
                        record.setCollaborationHubId(notification.getCollaborationHubId());
                        record.setPostId(notification.getPostId());
                        record.setCommentId(notification.getCommentId());
                        record.setChatChannelId(notification.getChatChannelId());
                        record.setBriefId(notification.getBriefId());
                        record.setMetadata(notification.getMetadata());
                        record.setCreatedAt(notification.getCreatedAt());
                        record.setReadAt(notification.getReadAt());
                        return record;
                    })
                    .collect(java.util.stream.Collectors.toList());

            int[] results = dsl.batchInsert(records).execute();

            int totalCreated = results.length;
            logger.info("Successfully bulk created {} mixed notifications", totalCreated);

            return totalCreated;

        } catch (DataAccessException e) {
            logger.error("Failed to bulk create {} mixed notifications: {}", notifications.size(), e.getMessage(), e);
            throw new NotificationStorageException("Failed to bulk create mixed notifications", e);
        }
    }

    // ========================================
    // VALIDATION HELPER METHODS
    // ========================================

    /**
     * Validates email pagination parameters.
     */
    private void validateEmailPaginationParameters(String email, int page, int size) {
        validateEmailParameter(email);
        if (page < 0) {
            throw new IllegalArgumentException("Page number must be non-negative (0-based pagination)");
        }
        if (size <= 0 || size > 100) {
            throw new IllegalArgumentException("Page size must be between 1 and 100");
        }
    }

    /**
     * Validates email parameter.
     */
    private void validateEmailParameter(String email) {
        if (email == null || email.trim().isEmpty()) {
            throw new IllegalArgumentException("Email cannot be null or empty");
        }
        if (!email.contains("@") || !email.contains(".")) {
            throw new IllegalArgumentException("Invalid email format: " + email);
        }
        if (email.length() > 255) {
            throw new IllegalArgumentException("Email exceeds maximum length of 255 characters");
        }
    }

    /**
     * Validates parameters for mark as read by email operation.
     */
    private void validateMarkAsReadByEmailParameters(Long notificationId, String email) {
        if (notificationId == null || notificationId <= 0) {
            throw new IllegalArgumentException("Notification ID must be positive");
        }
        validateEmailParameter(email);
    }

    /**
     * Validates mixed notifications for bulk operations.
     */
    private void validateMixedNotifications(List<Notification> notifications) {
        for (int i = 0; i < notifications.size(); i++) {
            Notification notification = notifications.get(i);
            if (notification == null) {
                throw new IllegalArgumentException("Notification at index " + i + " is null");
            }

            boolean hasUserId = notification.getUserId() != null;
            boolean hasEmail = notification.getEmail() != null && !notification.getEmail().trim().isEmpty();


            if (!hasUserId && !hasEmail) {
                throw new IllegalArgumentException("Notification at index " + i + " has neither user_id nor email (one required)");
            }

            // Validate email format if present
            if (hasEmail) {
                try {
                    validateEmailParameter(notification.getEmail());
                } catch (IllegalArgumentException e) {
                    throw new IllegalArgumentException("Notification at index " + i + " has invalid email: " + e.getMessage());
                }
            }
        }
    }
}
