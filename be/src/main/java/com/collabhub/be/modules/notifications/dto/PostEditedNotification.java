package com.collabhub.be.modules.notifications.dto;

import com.collabhub.be.modules.notifications.service.NotificationStorageService;
import com.collabhub.be.modules.notifications.service.NotificationTranslationService;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * Strongly-typed notification for post edit notifications.
 * 
 * <p>This notification is sent when a post that has been reviewed is edited by its creator.
 * It provides type-safe properties for all post edit data and generates
 * appropriate edit notification messages with deep links to the content.</p>
 * 
 * <h3>Usage Example:</h3>
 * <pre>
 * PostEditedNotification notification = PostEditedNotification.builder()
 *     .recipients(reviewers)
 *     .editorName("John Doe")
 *     .postTitle("Summer Campaign Content")
 *     .entityIds(123L, 456L)
 *     .build();
 * </pre>
 * 
 * <AUTHOR> Hub Team
 * @since 1.0.0
 */
public class PostEditedNotification extends BaseNotification {

    private static final Logger logger = LoggerFactory.getLogger(PostEditedNotification.class);

    // ========================================
    // NOTIFICATION-SPECIFIC PROPERTIES
    // ========================================

    @NotBlank(message = "Editor name is required for post edit notifications")
    private final String editorName;

    @NotBlank(message = "Post title is required for post edit notifications")
    private final String postTitle;

    private final String editSummary;

    // Entity IDs for deep linking
    private final Long hubId;
    private final Long postId;

    // ========================================
    // CONSTRUCTORS
    // ========================================

    /**
     * Creates a new builder for PostEditedNotification.
     * 
     * @return a new builder instance
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * Private constructor for builder pattern.
     */
    private PostEditedNotification(Builder builder) {
        super(builder.recipients, builder.urgency, builder.entityReferences, builder.locale);
        this.editorName = builder.editorName;
        this.postTitle = builder.postTitle;
        this.editSummary = builder.editSummary;
        this.hubId = builder.hubId;
        this.postId = builder.postId;
    }

    // ========================================
    // ABSTRACT METHOD IMPLEMENTATIONS
    // ========================================

    @Override
    public NotificationType getType() {
        return NotificationType.POST_EDITED;
    }

    @Override
    protected Map<String, Object> buildParameters() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("editorName", editorName);
        parameters.put("postTitle", postTitle);
        if (editSummary != null && !editSummary.trim().isEmpty()) {
            parameters.put("editSummary", editSummary.trim());
        }
        return parameters;
    }

    @Override
    public NotificationMetadata generateMetadata() {
        NotificationMetadata.Builder builder = NotificationMetadata.builder()
                .actorName(editorName)
                .targetTitle(postTitle)
                .actionContext("edited a post you reviewed")
                .deepLinkPath(String.format("/app/collaboration-hubs/%d?tab=posts&post=%d", hubId, postId));

        if (editSummary != null && !editSummary.trim().isEmpty()) {
            builder.commentPreview(editSummary.trim());
        }

        return builder.build();
    }

    // ========================================
    // VALIDATION
    // ========================================

    @Override
    protected void validateSpecific() {
        if (editorName == null || editorName.trim().isEmpty()) {
            throw new IllegalArgumentException("Editor name cannot be null or empty");
        }
        if (postTitle == null || postTitle.trim().isEmpty()) {
            throw new IllegalArgumentException("Post title cannot be null or empty");
        }
        if (hubId == null || hubId <= 0) {
            throw new IllegalArgumentException("Hub ID must be a positive number");
        }
        if (postId == null || postId <= 0) {
            throw new IllegalArgumentException("Post ID must be a positive number");
        }
    }

    // ========================================
    // GETTERS
    // ========================================

    public String getEditorName() {
        return editorName;
    }

    public String getPostTitle() {
        return postTitle;
    }

    public String getEditSummary() {
        return editSummary;
    }

    public Long getHubId() {
        return hubId;
    }

    public Long getPostId() {
        return postId;
    }

    // ========================================
    // BUILDER CLASS
    // ========================================

    /**
     * Builder class for PostEditedNotification.
     */
    public static class Builder {
        private List<NotificationRecipient> recipients;
        private NotificationUrgency urgency = NotificationUrgency.NORMAL; // Default for post edits
        private NotificationStorageService.EntityReferences entityReferences;
        private Locale locale;
        private String editorName;
        private String postTitle;
        private String editSummary;
        private Long hubId;
        private Long postId;

        private Builder() {}

        public Builder recipients(@NotEmpty List<NotificationRecipient> recipients) {
            this.recipients = recipients;
            return this;
        }

        public Builder urgency(@NotNull NotificationUrgency urgency) {
            this.urgency = urgency;
            return this;
        }

        public Builder entityReferences(NotificationStorageService.EntityReferences entityReferences) {
            this.entityReferences = entityReferences;
            return this;
        }

        public Builder locale(Locale locale) {
            this.locale = locale;
            return this;
        }

        public Builder editorName(@NotBlank String editorName) {
            this.editorName = editorName;
            return this;
        }

        public Builder postTitle(@NotBlank String postTitle) {
            this.postTitle = postTitle;
            return this;
        }

        public Builder editSummary(String editSummary) {
            this.editSummary = editSummary;
            return this;
        }

        public Builder hubId(@NotNull Long hubId) {
            this.hubId = hubId;
            return this;
        }

        public Builder postId(@NotNull Long postId) {
            this.postId = postId;
            return this;
        }

        /**
         * Convenience method to set entity IDs for hub and post.
         * 
         * @param hubId the collaboration hub ID
         * @param postId the post ID
         * @return this builder for method chaining
         */
        public Builder entityIds(@NotNull Long hubId, @NotNull Long postId) {
            this.hubId = hubId;
            this.postId = postId;
            return this;
        }

        /**
         * Builds the PostEditedNotification with validation.
         * 
         * @return a new PostEditedNotification instance
         * @throws IllegalArgumentException if required fields are missing or invalid
         */
        public PostEditedNotification build() {
            // Set entity references if not already set
            if (entityReferences == null && hubId != null && postId != null) {
                entityReferences = NotificationStorageService.EntityReferences.post(hubId, postId);
            }

            PostEditedNotification notification = new PostEditedNotification(this);
            notification.validate();
            return notification;
        }
    }
}
