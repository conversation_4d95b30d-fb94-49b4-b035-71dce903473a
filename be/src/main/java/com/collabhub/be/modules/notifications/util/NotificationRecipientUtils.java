package com.collabhub.be.modules.notifications.util;

import com.collabhub.be.modules.notifications.dto.NotificationRecipient;
import com.collabhub.be.modules.notifications.dto.UnifiedNotificationRecipient;
import org.jooq.generated.tables.pojos.HubParticipant;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

/**
 * Production-grade utility class for notification recipient management.
 *
 * <p>This utility class provides factory methods and conversion utilities for working
 * with notification recipients in the unified notification system. It handles the
 * complexity of converting between different data sources and recipient types.</p>
 *
 * <h3>Key Features:</h3>
 * <ul>
 *   <li>Type-safe conversion from various data sources</li>
 *   <li>Bulk operations for performance optimization</li>
 *   <li>Validation and error handling</li>
 *   <li>Recipient filtering and grouping utilities</li>
 * </ul>
 *
 * <AUTHOR> Hub Team
 * @since 1.0.0
 */
public final class NotificationRecipientUtils {

    private NotificationRecipientUtils() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }

    // ========================================
    // FACTORY METHODS
    // ========================================

    /**
     * Creates a UnifiedNotificationRecipient from a HubParticipant entity.
     *
     * <p>Automatically determines if the participant is internal (has user_id) or
     * external (email-only) and creates the appropriate unified recipient.</p>
     *
     * @param participant the hub participant (must not be null)
     * @return UnifiedNotificationRecipient instance
     * @throws IllegalArgumentException if participant is invalid or internal participants lack user data
     */
    public static UnifiedNotificationRecipient fromHubParticipant(@NotNull HubParticipant participant) {
        if (participant.getUserId() != null) {
            // Internal participant - need to get user data
            throw new IllegalArgumentException("Internal participants require User entity data. Use fromHubParticipantWithUser() instead.");
        } else {
            // External participant
            return UnifiedNotificationRecipient.fromHubParticipant(participant);
        }
    }

    // ========================================
    // BULK CONVERSION METHODS
    // ========================================

    /**
     * Converts a list of HubParticipant entities to UnifiedNotificationRecipients with User lookup.
     *
     * <p>This method handles mixed participants (both internal and external) and
     * performs bulk User entity loading for internal participants. This is the preferred
     * method for converting hub participants in service classes.</p>
     *
     * @param participants the list of participants (must not be null or empty)
     * @param userRepository the user repository for bulk loading internal users
     * @return list of NotificationRecipient instances
     * @throws IllegalArgumentException if any participant is invalid
     */
    public static List<NotificationRecipient> convertHubParticipantsToRecipients(
            @NotEmpty List<HubParticipant> participants,
            @NotNull com.collabhub.be.modules.auth.repository.UserRepository userRepository) {

        if (participants.isEmpty()) {
            return List.of();
        }

        List<UnifiedNotificationRecipient> recipients = new ArrayList<>();
        List<Long> internalUserIds = new ArrayList<>();

        // Separate internal and external participants
        for (HubParticipant participant : participants) {
            if (participant.getUserId() != null) {
                // Internal participant - collect user ID for bulk loading
                internalUserIds.add(participant.getUserId());
            } else {
                // External participant - create recipient directly
                recipients.add(UnifiedNotificationRecipient.fromHubParticipant(participant));
            }
        }

        // Bulk load internal users if any
        if (!internalUserIds.isEmpty()) {
            List<org.jooq.generated.tables.pojos.User> users = userRepository.findByIds(internalUserIds);
            recipients.addAll(users.stream()
                                  .map(UnifiedNotificationRecipient::fromUser)
                                  .toList());
        }

        return new ArrayList<>(recipients);
    }

    // ========================================
    // VALIDATION AND UTILITY METHODS
    // ========================================

    /**
     * Validates a list of recipients, ensuring all are properly formed.
     *
     * @param recipients the recipient list to validate (must not be null)
     * @throws IllegalArgumentException if any recipient is invalid
     */
    public static void validateRecipients(@NotNull List<NotificationRecipient> recipients) {
        if (recipients.isEmpty()) {
            throw new IllegalArgumentException("Recipient list cannot be empty");
        }
        
        for (int i = 0; i < recipients.size(); i++) {
            NotificationRecipient recipient = recipients.get(i);
            if (recipient == null) {
                throw new IllegalArgumentException("Recipient at index " + i + " is null");
            }
            
            try {
                recipient.validate();
            } catch (Exception e) {
                throw new IllegalArgumentException("Recipient at index " + i + " is invalid: " + e.getMessage(), e);
            }
        }
    }

}
