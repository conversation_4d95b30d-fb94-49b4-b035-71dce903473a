package com.collabhub.be.modules.notifications.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;

/**
 * Configuration properties for notification batching.
 * Controls batch processing behavior, retry logic, distributed locking, and cleanup operations.
 *
 * <p>These properties are used by {@link com.collabhub.be.modules.notifications.service.NotificationBatchProcessor}
 * to control the timing and behavior of batch notification processing.</p>
 */
@Component
@ConfigurationProperties(prefix = "app.notifications.batching")
@Validated
public class NotificationBatchingProperties {

    /**
     * Whether notification batching is enabled.
     * When disabled, all notifications are sent immediately.
     */
    private boolean enabled = true;

    /**
     * Batch processing interval in seconds.
     * How often the batch processor runs to send accumulated notifications.
     */
    @NotNull
    @Min(30)
    private Integer processIntervalSeconds = 180; // 3 minutes

    /**
     * Retention period in hours for processed batch queue entries.
     * Entries older than this are cleaned up.
     */
    @NotNull
    @Min(1)
    private Integer cleanupRetentionHours = 24;

    /**
     * Maximum number of retry attempts for failed batch deliveries.
     */
    @NotNull
    @Min(0)
    private Integer maxRetryAttempts = 3;

    /**
     * Delay in minutes between retry attempts.
     */
    @NotNull
    @Min(1)
    private Integer retryDelayMinutes = 5;

    /**
     * Lock timeout in minutes for distributed batch processing.
     * Prevents stuck locks from blocking processing.
     */
    @NotNull
    @Min(1)
    private Integer lockTimeoutMinutes = 10;

    // Getters and setters

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public Integer getProcessIntervalSeconds() {
        return processIntervalSeconds;
    }

    public void setProcessIntervalSeconds(Integer processIntervalSeconds) {
        this.processIntervalSeconds = processIntervalSeconds;
    }

    public Integer getCleanupRetentionHours() {
        return cleanupRetentionHours;
    }

    public void setCleanupRetentionHours(Integer cleanupRetentionHours) {
        this.cleanupRetentionHours = cleanupRetentionHours;
    }

    public Integer getMaxRetryAttempts() {
        return maxRetryAttempts;
    }

    public void setMaxRetryAttempts(Integer maxRetryAttempts) {
        this.maxRetryAttempts = maxRetryAttempts;
    }

    public Integer getRetryDelayMinutes() {
        return retryDelayMinutes;
    }

    public void setRetryDelayMinutes(Integer retryDelayMinutes) {
        this.retryDelayMinutes = retryDelayMinutes;
    }

    public Integer getLockTimeoutMinutes() {
        return lockTimeoutMinutes;
    }

    public void setLockTimeoutMinutes(Integer lockTimeoutMinutes) {
        this.lockTimeoutMinutes = lockTimeoutMinutes;
    }
}
